{"extends": ["next/core-web-vitals", "airbnb", "airbnb/hooks", "airbnb-typescript", "prettier", "plugin:storybook/recommended"], "parserOptions": {"project": ["./tsconfig.json", "./jsconfig.json"]}, "rules": {"react/react-in-jsx-scope": 0, "react/jsx-props-no-spreading": 0, "react/require-default-props": 0, "import/prefer-default-export": 0, "jsx-a11y/click-events-have-key-events": 0, "jsx-a11y/no-static-element-interactions": 0, "react/function-component-definition": 0, "no-restricted-syntax": 0, "@typescript-eslint/no-loop-func": 0, "@typescript-eslint/no-shadow": 0, "no-nested-ternary": 0, "react/jsx-no-useless-fragment": 0, "@typescript-eslint/naming-convention": 0, "no-plusplus": 0, "arrow-body-style": 0, "prefer-const": 0, "@typescript-eslint/no-unused-vars": 0, "object-shorthand": 0, "prefer-template": 0}}