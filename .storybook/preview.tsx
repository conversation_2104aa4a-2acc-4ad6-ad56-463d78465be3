import React from "react";
import type { Preview } from "@storybook/react";
import { iranSansXPro } from "../src/providers/fonts";
import "../src/styles/globals.css";
import { NextIntlClientProvider } from "next-intl";
import defaultMessages from "../src/constants/locals/fa.json";
import ActionSheetProvider from "../src/components/ui/actionSheet/ActionSheetProvider.tsx";

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i
      }
    }
  },
  decorators: [
    Story => (
      <div className={`${iranSansXPro.variable} font-iran-yekan`} dir="rtl">
        <NextIntlClientProvider
          locale="en"
          messages={defaultMessages}
          // ... potentially other config
        >
          <Story />
          <ActionSheetProvider />
        </NextIntlClientProvider>
      </div>
    )
  ]
};

export default preview;
