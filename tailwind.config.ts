import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: "class",
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}"
  ],
  theme: {
    extend: {
      backgroundImage: {
        smGradient: "linear-gradient(90deg, #111723 0%, #273349 100%)",
        instagram:" linear-gradient(90deg, #B00EA3 23.5%, #194399 100%)"
      },
      colors: {
        global: {
          "emerald-400": "rgb(var(--color-global-emerald-400) / <alpha-value>)",
          "orange-500": "rgb(var(--color-global-orange-500) / <alpha-value>)",
          white: "rgb(var(--color-global-white) / <alpha-value>)"
        },
        "background-secondary": "rgb(var(--color-background-secondary) / <alpha-value>)",
        "background-base": {
          white: "rgb(var(--color-background-base-white) / <alpha-value>)"
        },
        brand: {
          primary: "rgb(var(--color-brand-primary) / <alpha-value>)"
        },
        gray: {
          20: "rgb(var(--color-gray-20) / <alpha-value>)",
          40: "rgb(var(--color-gray-40) / <alpha-value>)",
          200: "rgb(var(--color-gray-200) / <alpha-value>)"
        },
        semantic: {
          "neutral-400": "rgb(var(--color-semantic-neutral-400) / <alpha-value>)",
          "neutral-200": "rgb(var(--color-semantic-neutral-200) / <alpha-value>)"
        },
        surface: {
          primary: "rgb(var(--color-surface-primary) / <alpha-value>)",
          info: "rgb(var(--color-surface-info) / <alpha-value>)",
          "action-light": "rgb(var(--color-surface-action-light) / <alpha-value>)",
          "action-hover": "rgb(var(--color-surface-action-hover) / <alpha-value>)",
          "action-disable": "rgb(var(--color-surface-action-disable) / <alpha-value>)",
          secondary: "rgb(var(--color-surface-secondary) / <alpha-value>)",
          "secondary-light": "rgb(var(--color-surface-secondary-light) / <alpha-value>)",
          "warining-1": "rgb(var(--color-surface-warining-1) / <alpha-value>)",
          "warining-2": "rgb(var(--color-surface-warining-2) / <alpha-value>)",
          "warining-hover": "rgb(var(--color-surface-warining-hover) / <alpha-value>)",
          thertiary: "rgb(var(--color-surface-thertiary) / <alpha-value>)",
          inverse: "rgb(var(--color-surface-inverse) / <alpha-value>)"
        },
        content: {
          "on-action-disable": "rgb(var(--color-content-on-action-disable) / <alpha-value>)",
          "on-action-2": "rgb(var(--color-content-on-action-2) / <alpha-value>)",
          "on-action-hover-1": "rgb(var(--color-content-on-action-hover-1) / <alpha-value>)",
          "on-action-hover-2": "rgb(var(--color-content-on-action-hover-2) / <alpha-value>)",
          "on-info": "rgb(var(--color-content-on-info) / <alpha-value>)",
          "on-info-2": "rgb(var(--color-content-on-info-2) / <alpha-value>)",
          primary: "rgb(var(--color-content-primary) / <alpha-value>)",
          secondary: "rgb(var(--color-content-secondary) / <alpha-value>)",
          subtle: "rgb(var(--color-content-subtle) / <alpha-value>)",
          "secondary-light": "rgb(var(--color-content-secondary-light) / <alpha-value>)",
          tertiary: "rgb(var(--color-content-tertiary) / <alpha-value>)",
          "on-warning-disable": "rgb(var(--color-content-on-warning-disable) / <alpha-value>)",
          "on-warning-2": "rgb(var(--color-content-on-warning-2) / <alpha-value>)",
          "on-error-2": "rgb(var(--color-content-on-error-2) / <alpha-value>)",
          "on-success-2": "rgb(var(--color-content-on-success-2) / <alpha-value>)",
          "disable": "rgb(var(--color-content-disable) / <alpha-value>)",
        },
        border: {
          secondary: "rgb(var(--color-border-secondary) / <alpha-value>)",
          "secondary-light": "rgb(var(--color-border-secondary-light) / <alpha-value>)",
          tertiary: "rgb(var(--color-border-tertiary) / <alpha-value>)",
          primary: "rgb(var(--color-border-primary) / <alpha-value>)",
          "primary-lightest": "rgb(var(--color-border-primary-lightest) / <alpha-value>)"
        }
      },
      fontFamily: {
        "iran-yekan": ["var(--font-iran-yekan)"]
      },
      fontSize: {
        10: "0.625rem", // 10px
        13: "0.8125rem", // 13px
        15: "0.938rem", // 15px
        32: "2rem"
      },
      screens: {
        xxs: "320px",
        xmd: "350px",
        xs: "400px"
      },
      boxShadow: {
        md: "0px 2px 4px -1px rgba(18, 18, 23, 0.06), 0px 4px 6px -1px rgba(18, 18, 23, 0.08)",
        sm: "0px 4px 15px 0px #0000001A"
      },
      spacing: {
        "13px": "0.813rem",
        "7px": "0.438rem"
      }
    }
  }
  // plugins: [
  //   function ({ addUtilities, theme }: any) {
  //     const newUtilities = {
  //       ".text-h1-bold": {
  //         fontSize: "28px",
  //         fontWeight: "700"
  //       },
  //       ".text-h1-heavy": {
  //         fontSize: "28px",
  //         fontWeight: "800"
  //       },
  //       ".text-h2-bold": {
  //         fontSize: "24px",
  //         fontWeight: "700"
  //       },
  //       ".text-h2-heavy": {
  //         fontSize: "24px",
  //         fontWeight: "800"
  //       },
  //       ".text-h3-bold": {
  //         fontSize: "22px",
  //         fontWeight: "700"
  //       },
  //       ".text-h3-medium": {
  //         fontSize: "22px",
  //         fontWeight: "500"
  //       },
  //       ".text-h4-bold": {
  //         fontSize: "20px",
  //         fontWeight: "700"
  //       },
  //       ".text-h4-medium": {
  //         fontSize: "20px",
  //         fontWeight: "500"
  //       },
  //       ".text-h5-bold": {
  //         fontSize: "18px",
  //         fontWeight: "700"
  //       },
  //       ".text-h5-medium": {
  //         fontSize: "18px",
  //         fontWeight: "500"
  //       },
  //       ".text-subtitle-bold": {
  //         fontSize: "16px",
  //         fontWeight: "700"
  //       },
  //       ".text-subtitle-medium": {
  //         fontSize: "16px",
  //         fontWeight: "500"
  //       },
  //       ".text-body1-bold": {
  //         fontSize: "15px",
  //         fontWeight: "700"
  //       },
  //       ".text-body1-regular": {
  //         fontSize: "15px",
  //         fontWeight: "400"
  //       },
  //       ".text-body1-medium": {
  //         fontSize: "15px",
  //         fontWeight: "500"
  //       },
  //       ".text-body2-bold": {
  //         fontSize: "14px",
  //         fontWeight: "700"
  //       },
  //       ".text-body2-regular": {
  //         fontSize: "14px",
  //         fontWeight: "400"
  //       },
  //       ".text-body2-medium": {
  //         fontSize: "14px",
  //         fontWeight: "500"
  //       },
  //       ".text-body3-regular": {
  //         fontSize: "13px",
  //         fontWeight: "400"
  //       },
  //       ".text-body3-medium": {
  //         fontSize: "13px",
  //         fontWeight: "500"
  //       },
  //       ".text-body4-bold": {
  //         fontSize: "12px",
  //         fontWeight: "700"
  //       },
  //       ".text-body4-regular": {
  //         fontSize: "12px",
  //         fontWeight: "400"
  //       },
  //       ".text-body4-medium": {
  //         fontSize: "12px",
  //         fontWeight: "500"
  //       },
  //       ".text-caption-regular": {
  //         fontSize: "10px",
  //         fontWeight: "400"
  //       },
  //       ".text-caption-bold": {
  //         fontSize: "10px",
  //         fontWeight: "700"
  //       },
  //       ".text-caption-medium": {
  //         fontSize: "10px",
  //         fontWeight: "500"
  //       }
  //     };
  //     addUtilities(newUtilities, ["responsive", "hover"]);
  //   }
  // ]
};
export default config;
