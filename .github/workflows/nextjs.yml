name: Shop Builder Storefront CI

on:
  push:
    branches:
      - develop
      - master
  workflow_dispatch:

jobs:
  build_stage:
    environment: staging
    name: run staging build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Login to Docker Registry
        run: echo "${{ secrets.ACCESS_TOKEN }}" | docker login ${{ vars.DOCKER_REGISTRY }} -u ${{ github.actor }} --password-stdin
      - name: Build Images
        run: |
          DOCKER_REGISTRY=${{ vars.DOCKER_REGISTRY }} \
          REPOSITORY_OWNER=${{ github.repository_owner }} \
          PROJECT_NAME=${{ vars.PROJECT_NAME }} \
          PORT=${{ vars.PORT }} \
          NEXT_PUBLIC_BASE_URL=${{ vars.NEXT_PUBLIC_BASE_URL }} \
          BUILD_TAG=dev \
          docker compose --project-name ${{ vars.PROJECT_NAME }} build --push
  deploy_stage:
    needs: build_stage
    environment: staging
    name: run staging deploy
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Copy Docker Compose file
        uses: appleboy/scp-action@v0.1.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          proxy_host: ${{ secrets.SSH_PROXY_HOST }}
          proxy_username: ${{ secrets.SSH_PROXY_USER }}
          proxy_key: ${{ secrets.SSH_PROXY_PRIVATE_KEY }}
          proxy_passphrase: ${{ secrets.SSH_PROXY_PASSPHRASE }}
          port: 22
          timeout: 120s
          proxy_timeout: 120s
          source: "./docker-compose.yml"
          target: "~/${{ vars.PROJECT_NAME }}"
      - name: Deploy
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          proxy_host: ${{ secrets.SSH_PROXY_HOST }}
          proxy_username: ${{ secrets.SSH_PROXY_USER }}
          proxy_key: ${{ secrets.SSH_PROXY_PRIVATE_KEY }}
          proxy_passphrase: ${{ secrets.SSH_PROXY_PASSPHRASE }}
          port: 22
          timeout: 120s
          proxy_timeout: 120s
          script: |
            echo "${{ secrets.ACCESS_TOKEN }}" | docker login ${{ vars.DOCKER_REGISTRY }} -u ${{ github.actor }} --password-stdin

            cd ~/${{ vars.PROJECT_NAME }}
            rm .env
            touch .env
            echo BUILD_TAG=dev >> .env
            echo DOCKER_REGISTRY=${{ vars.DOCKER_REGISTRY }} >> .env
            echo REPOSITORY_OWNER=${{ github.repository_owner }} >> .env
            echo PROJECT_NAME=${{ vars.PROJECT_NAME }} >> .env
            echo PORT=${{ vars.PORT }} >> .env
            echo NEXT_PUBLIC_BASE_URL=${{ vars.NEXT_PUBLIC_BASE_URL }} >> .env
            docker compose pull
            docker compose --project-name ${{ vars.PROJECT_NAME }} up -d
            docker system prune -f
  
  build_production:
    environment: production
    name: run production build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Login to Docker Registry
        run: echo "${{ secrets.ACCESS_TOKEN }}" | docker login ${{ vars.DOCKER_REGISTRY }} -u ${{ github.actor }} --password-stdin
      - name: Build Images
        run: |
          DOCKER_REGISTRY=${{ vars.DOCKER_REGISTRY }} \
          REPOSITORY_OWNER=${{ github.repository_owner }} \
          PROJECT_NAME=${{ vars.PROJECT_NAME }} \
          PORT=${{ vars.PORT }} \
          NEXT_PUBLIC_BASE_URL=${{ vars.NEXT_PUBLIC_BASE_URL }} \
          BUILD_TAG=latest \
          docker compose --project-name ${{ vars.PROJECT_NAME }} build --push
  deploy_production:
    needs: build_production
    environment: production
    name: run production deploy
    if: github.ref == 'refs/heads/master'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Copy Docker Compose file
        uses: appleboy/scp-action@v0.1.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          proxy_host: ${{ secrets.SSH_PROXY_HOST }}
          proxy_username: ${{ secrets.SSH_PROXY_USER }}
          proxy_key: ${{ secrets.SSH_PROXY_PRIVATE_KEY }}
          proxy_passphrase: ${{ secrets.SSH_PROXY_PASSPHRASE }}
          port: 22
          timeout: 120s
          proxy_timeout: 120s
          source: "./docker-compose.yml"
          target: "~/${{ vars.PROJECT_NAME }}"
      - name: Deploy
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          proxy_host: ${{ secrets.SSH_PROXY_HOST }}
          proxy_username: ${{ secrets.SSH_PROXY_USER }}
          proxy_key: ${{ secrets.SSH_PROXY_PRIVATE_KEY }}
          proxy_passphrase: ${{ secrets.SSH_PROXY_PASSPHRASE }}
          port: 22
          timeout: 120s
          proxy_timeout: 120s
          script: |
            echo "${{ secrets.ACCESS_TOKEN }}" | docker login ${{ vars.DOCKER_REGISTRY }} -u ${{ github.actor }} --password-stdin

            cd ~/${{ vars.PROJECT_NAME }}
            rm .env
            touch .env
            echo BUILD_TAG=latest >> .env
            echo DOCKER_REGISTRY=${{ vars.DOCKER_REGISTRY }} >> .env
            echo REPOSITORY_OWNER=${{ github.repository_owner }} >> .env
            echo PROJECT_NAME=${{ vars.PROJECT_NAME }} >> .env
            echo PORT=${{ vars.PORT }} >> .env
            echo NEXT_PUBLIC_BASE_URL=${{ vars.NEXT_PUBLIC_BASE_URL }} >> .env
            docker compose pull
            docker compose --project-name ${{ vars.PROJECT_NAME }} up -d
            docker system prune -f
