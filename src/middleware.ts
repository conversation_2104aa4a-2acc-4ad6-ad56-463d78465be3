import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { STORE_HANDLE } from "@/constants/cookies";
import { STORE_HANDLE_REGEX } from "@/constants/regexs";

export const getEnvPath = (devPath: string, prodPath: string) =>
  process.env.NODE_ENV === "development" ? devPath : prodPath;

export async function middleware(req: NextRequest) {
  const url = req.nextUrl.clone();
  const { pathname, search } = url;

  /* -------------- Check if current path contains a store handle ------------- */
  const currentPathStoreHandle = pathname.match(STORE_HANDLE_REGEX)?.[0];

  /* ---------------------- Get store handle from cookie ---------------------- */
  let cookieStoreHandle: string | null = req.cookies.get(STORE_HANDLE)?.value || null;

  // Fallback: Parse cookie manually from header if not found
  if (!cookieStoreHandle) {
    const cookieHeader = req.headers.get("cookie") || "";
    const match = cookieHeader.match(new RegExp(`${STORE_HANDLE}=([^;]*)`));
    cookieStoreHandle = match?.[1] || null;
  }

  // If there's a store handle in the current path, continue
  if (currentPathStoreHandle) {
    return NextResponse.next();
  }

  // If cookie exists and current path doesn't start with it, redirect
  if (cookieStoreHandle && !pathname.startsWith(`/${cookieStoreHandle}`)) {
    const newPathname = `/${cookieStoreHandle}${pathname}${search}`;
    return NextResponse.redirect(new URL(newPathname, req.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico|manifest.json|service-worker.js|manifest.webmanifest|assets|images).*)"
  ]
};
