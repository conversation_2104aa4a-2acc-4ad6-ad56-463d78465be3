/* eslint-disable react/require-default-props */
import React from "react";
import { twMerge } from "tailwind-merge";
import styles from "./TextArea.module.scss";

export type TInput = React.DetailedHTMLProps<React.InputHTMLAttributes<HTMLTextAreaElement>, HTMLTextAreaElement> & {
  className?: string;
};

function Input({ className = undefined, ...restProps }: TInput) {
  return (
    <textarea
      className={twMerge(
        "w-full resize-none bg-transparent px-3 py-3.5 text-sm leading-5 outline-none",
        styles.root,
        className
      )}
      {...restProps}
    />
  );
}

export default Input;
