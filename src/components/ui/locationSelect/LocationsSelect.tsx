import React, { useState, use<PERSON>emo, use<PERSON>ffect, FocusEventHandler } from "react";
import { useCombobox } from "downshift";
import { useTranslations } from "next-intl";
import ArrowDown from "@/assets/icons/arrow-down-gray.svg";
import { TInputWrapper } from "../inputWrapper/InputWrapper";
import Input from "../input";
import useLocations from "./useLocations";
import InputWrapper from "../inputWrapper";

// Define TypeScript interfaces
export interface ILocationsData {
  id: string;
  iso2: string;
  iso3: string;
  name: string;
  sub_locations: ILocationsData[];
  type: "Country" | "State" | "City";
}

export interface ILocationsResponse {
  data: ILocationsData[];
  status: string;
}

interface FlattenedItem {
  id: string;
  type: "Country" | "State" | "City";
  value: string;
  parentName?: string;
  selectable: boolean;
}

interface GroupedItem {
  parent: string;
  parentId: string;
  children: FlattenedItem[];
  parentItem: FlattenedItem | null;
}

interface GroupedDropdownProps {
  inputWrapperProps?: TInputWrapper;
  label?: string;
  value?: string;
  placeholder?: string;
  onBlur?: FocusEventHandler<any>;
  onSelect?: (item: FlattenedItem) => void;
}

const LocationSelect: React.FC<GroupedDropdownProps> = ({
  placeholder,
  onSelect,
  value,
  inputWrapperProps,
  label,
  onBlur
}) => {
  const t = useTranslations();
  const [inputValue, setInputValue] = useState<string>("");
  // Add a separate state for search
  const [searchValue, setSearchValue] = useState<string>("");

  // Get locations data from custom hook
  const { countries } = useLocations();

  // Memoize the flattened items to prevent unnecessary recalculations
  const flattenedItems = useMemo(() => {
    if (!countries?.data || countries.data.length === 0) {
      return [];
    }

    const items: FlattenedItem[] = [];

    // Recursive function to process all location levels
    const processLocation = (location: ILocationsData, parentName?: string) => {
      // Add the location itself - only city type items are selectable
      items.push({
        id: location.id,
        type: location.type,
        value: location.name,
        parentName,
        selectable: location.type === "City" // Only cities are selectable
      });

      // Process sub-locations if any
      if (location.sub_locations && location.sub_locations.length > 0) {
        location.sub_locations.forEach(subLocation => {
          processLocation(subLocation, location.name);
        });
      }
    };

    // Start processing from top-level countries
    countries.data.forEach(country => {
      processLocation(country);
    });

    return items;
  }, [countries?.data]);

  const defaultValue = flattenedItems?.find(item => item?.id === value);

  const [selectedItem, setSelectedItem] = useState<FlattenedItem | null>(defaultValue || null);

  useEffect(() => {
    setSelectedItem(defaultValue || null);
    // If we have a default value, set the input value to display it
    if (defaultValue) {
      setInputValue(defaultValue.value);
    }
  }, [defaultValue]);

  // Memoize filtered items based on search value for better performance
  const filteredItems = useMemo(() => {
    if (!searchValue) {
      return flattenedItems;
    }

    const searchTerm = searchValue.toLowerCase();
    return flattenedItems.filter(item => item.value.toLowerCase().includes(searchTerm));
  }, [flattenedItems, searchValue]);

  // Filter only the selectable items for downshift
  const selectableItems = useMemo(() => filteredItems.filter(item => item.selectable), [filteredItems]);

  const { isOpen, getMenuProps, getInputProps, getToggleButtonProps, highlightedIndex, getItemProps, openMenu } =
    useCombobox({
      items: selectableItems,
      inputValue: searchValue,
      selectedItem,
      itemToString: item => (item ? item.value : ""),
      onInputValueChange: ({ inputValue }) => {
        setSearchValue(inputValue || "");
      },
      onSelectedItemChange: ({ selectedItem }) => {
        if (selectedItem) {
          setSelectedItem(selectedItem);
          setInputValue(selectedItem.value);
          // Clear the search when an item is selected
          setSearchValue("");
          if (onSelect) {
            onSelect(selectedItem);
          }
        }
      },
      // Clear search value when the menu closes
      onStateChange: ({ type }) => {
        if (
          [
            useCombobox.stateChangeTypes?.InputBlur,
            useCombobox.stateChangeTypes?.InputClick,
            useCombobox.stateChangeTypes?.InputKeyDownEnter
          ].includes(type as any)
        ) {
          setSearchValue("");
        }
      }
    });

  // Group the filtered items by parent for display
  // Memoize this calculation for performance
  const groupedItems = useMemo(() => {
    if (filteredItems.length === 0) {
      return [];
    }

    const groupMap: Record<string, GroupedItem> = {};

    // First pass: create parent entries and categorize items
    filteredItems.forEach(item => {
      if (item.type === "Country" || item.type === "State") {
        // This is a parent item
        if (!groupMap[item.id]) {
          groupMap[item.id] = { parent: item.value, parentId: item.id, children: [], parentItem: item };
        }
      } else if (item.parentName) {
        // Find the parent ID for this child
        const parentItem = flattenedItems.find(
          parent => parent.value === item.parentName && (parent.type === "Country" || parent.type === "State")
        );

        if (parentItem) {
          if (!groupMap[parentItem.id]) {
            groupMap[parentItem.id] = { parent: parentItem.value, parentId: parentItem.id, children: [], parentItem };
          }

          // Add this item as a child
          groupMap[parentItem.id].children.push(item);
        }
      }
    });

    // Convert map to array and sort by parent name
    return Object.values(groupMap)
      .filter(group => group.children.length > 0) // Only include groups with children
      .sort((a, b) => a.parent.localeCompare(b.parent));
  }, [filteredItems, flattenedItems]);

  // Handle input focus to open the menu
  const handleInputFocus = () => {
    // Update search value with input value for initial search
    setSearchValue(inputValue);
    openMenu();
  };

  return (
    <div className="relative w-full">
      {/* Combined input for selection and search */}
      <div className="relative">
        <div className="flex flex-col gap-1">
          {label && <span className="text-13 font-medium text-content-tertiary">{label}</span>}
          <InputWrapper
            {...inputWrapperProps}
            endAdornment={
              <div {...getToggleButtonProps()}>
                <ArrowDown />
              </div>
            }
          >
            <Input
              {...getInputProps({
                // onFocus: handleInputFocus,
                // کامنت شد. باعث میشود با کلیک روی اینپوت دراپ داون باز ولی سریعا بسته شود و باید چندبار کلیک کرد تا باز بماند
                onBlur,
                // Show selected value when dropdown is closed, show search value when open
                value: isOpen ? searchValue : inputValue,
                onChange: e => {
                  // This is handled by downshift's getInputProps
                  // Just updating the searchValue for filtering
                  setSearchValue((e.target as any)?.value);
                }
              })}
              placeholder={placeholder}
            />
          </InputWrapper>
        </div>
      </div>

      {/* Dropdown menu with grouped items */}
      <ul
        {...getMenuProps()}
        className={`absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded border border-gray-300 bg-white shadow-lg ${
          isOpen ? "block" : "hidden"
        }`}
      >
        {isOpen &&
          groupedItems.map(group => (
            <li key={group.parentId} className="relative">
              {/* Parent header - sticky */}
              <div className="sticky top-0 z-10 border-b border-b-gray-20 bg-gray-50 px-3 py-2 font-bold text-gray-800">
                {group.parent}
              </div>

              {/* Children items - only these are selectable */}
              {group.children.length > 0 && (
                <ul className="pl-4">
                  {group.children
                    .filter(child => child.selectable)
                    .map(childItem => {
                      const childIndex = selectableItems.indexOf(childItem);

                      return childIndex >= 0 ? (
                        <li
                          key={childItem.id}
                          {...getItemProps({ item: childItem, index: childIndex })}
                          className={`cursor-pointer px-3 py-1 text-sm ${
                            highlightedIndex === childIndex ? "bg-blue-100" : ""
                          } ${selectedItem?.id === childItem.id ? "font-bold" : ""}`}
                        >
                          {childItem.value}
                        </li>
                      ) : null;
                    })}
                </ul>
              )}
            </li>
          ))}

        {isOpen && selectableItems.length === 0 && (
          <li className="p-3 text-center text-sm text-gray-500">{t("general.noResultSearch")}</li>
        )}
      </ul>
    </div>
  );
};

export default LocationSelect;
