import { useLocationsQuery } from "@/services/apis/locations/locations";
import { ILocationsData, ILocationsResponse } from "@/services/apis/locations/types";

/**
 * Searches recursively through locations data for an item with the specified ID
 * @param locations - Array of location data to search through
 * @param id - The ID to search for
 * @returns The matching location object or undefined if not found
 */
export const findLocationById = (locations: ILocationsData[], id: string): ILocationsData | undefined => {
  // Base case: empty array
  if (!locations || locations.length === 0) {
    return undefined;
  }

  // First, check the current level
  const directMatch = locations.find(location => location.id === id);
  if (directMatch) {
    return directMatch;
  }

  // If not found, search in sub_locations
  for (const location of locations) {
    if (location.sub_locations && location.sub_locations.length > 0) {
      const nestedMatch = findLocationById(location.sub_locations, id);
      if (nestedMatch) {
        return nestedMatch;
      }
    }
  }

  // If we get here, no match was found
  return undefined;
};

/**
 * Searches for a location by ID within a locations response object
 * @param response - The locations response object
 * @param id - The ID to search for
 * @returns The matching location object or undefined if not found
 */
export const findLocationInResponse = (response?: ILocationsResponse, id?: string): ILocationsData | undefined => {
  if (!response || !response.data || !id) {
    return undefined;
  }

  return findLocationById(response.data, id);
};

export default function useLocations() {
  const { data: countries, isLoading } = useLocationsQuery();

  const getLocation = (id: string) => findLocationInResponse(countries, id);

  return { isLoading, countries, getLocation };
}
