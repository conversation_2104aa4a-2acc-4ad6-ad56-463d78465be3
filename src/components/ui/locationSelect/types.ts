import { ILocationsData } from "@/services/apis/locations/types";
import { FocusEventHandler } from "react";

export type TIranCities = {
  id: number | string;
  name: string;
  iso3?: string;
  iso2?: string;
  currency?: string;
  latitude?: number;
  longitude?: number;
  parentKey?: string;
  subGroup?: string;
};

export type TLocationSelectProps<T extends boolean> = {
  multiple?: T;
  inputValue?: string;
  hasEndAdornment?: boolean;
  placeholder?: string;
  label?: string;
  optional?: boolean;
  getType?: "name" | "id";
  value?: T extends true ? string[] | number[] : string | number;
  name?: string;
  helperText?: React.ReactNode;
  error?: boolean;
  handleBlur?: FocusEventHandler;
  onChange: (
    value: T extends true ? string[] | number[] : string | number,
    valueObj: T extends true ? TIranCities[] : TIranCities
  ) => void;
  select?: ILocationsData["type"];
  className?: string;
  excludeIds?: string[];
  hasAllCities?: boolean;
  requiredStar?: boolean;

  /**
   * @default true
   */
  disablePortal?: boolean;
};
