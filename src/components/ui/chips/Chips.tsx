/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable react/require-default-props */
import { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

function Chips({
  className = undefined,
  children,
  onClick
}: {
  className?: string;
  children?: ReactNode;
  onClick?: () => void;
}) {
  return (
    <div
      className={twMerge(
        "w-fit rounded-full border border-border-primary bg-surface-secondary px-3 py-1 text-xs font-normal text-content-primary",
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  );
}

export default Chips;
