/* eslint-disable no-console */
import SwitchTabs from "@/components/ui/switchTabs";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof SwitchTabs> = {
  component: SwitchTabs,
  title: "Components/ui/SwitchTabs"
};

export default meta;
type Story = StoryObj<typeof SwitchTabs>;

// 👇 Throws a type error it the args don't match the component props
export const Default: Story = {
  args: {
    tabs: [
      { id: 1, label: "تب اول" },
      { id: 2, label: "تب دوم با لیبل طولانی" },
      { id: 3, label: "تب سوم" }
    ],
    activeItemId: 2,
    onChange: id => {
      console.log("active tab: ", id);
    }
  }
};
