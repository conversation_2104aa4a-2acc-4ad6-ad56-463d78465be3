/* eslint-disable react/button-has-type */
import { useEffect, useRef, useState } from "react";
import { twMerge } from "tailwind-merge";

function SwitchTabs({
  className = undefined,
  tabs,
  activeItemId,
  onChange
}: {
  className?: string;
  tabs: { id: string | number; label: string }[];
  activeItemId?: string | number;
  onChange?: (id: string | number) => void;
}) {
  const [activeTab, setActiveTab] = useState(activeItemId || tabs[0].id);
  const tabRefs = useRef<Record<string | number, HTMLButtonElement | null>>({});
  const activeTabRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const updateActiveTabPosition = () => {
    setTimeout(() => {
      if (activeTabRef.current && tabRefs.current[activeTab] && containerRef.current) {
        const activeTabButton = tabRefs.current[activeTab];
        const containerRect = containerRef.current.getBoundingClientRect();
        const activeTabRect = activeTabButton.getBoundingClientRect();

        activeTabRef.current.style.width = `${activeTabRect.width}px`;
        activeTabRef.current.style.left = `${activeTabRect.left - containerRect.left}px`;
      }
    }, 0);
  };

  useEffect(() => {
    updateActiveTabPosition();
    window.addEventListener("resize", updateActiveTabPosition);
    return () => window.removeEventListener("resize", updateActiveTabPosition);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]);

  useEffect(() => {
    if (activeItemId !== undefined && activeItemId !== activeTab) {
      setActiveTab(activeItemId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeItemId]);

  const handleTabClick = (id: string | number) => {
    setActiveTab(id);
    if (onChange) {
      onChange(id);
    }
  };

  return (
    <div className={twMerge("relative", className)} ref={containerRef}>
      <div className="flex justify-center rounded-xl bg-surface-secondary p-2">
        {tabs.map(tab => (
          <button
            key={tab.id}
            ref={el => {
              tabRefs.current[tab.id] = el;
            }}
            onClick={() => handleTabClick(tab.id)}
            className={twMerge(
              "relative z-10 h-10 rounded-md px-3 text-sm font-medium outline-none transition-colors duration-300",
              activeTab === tab.id ? "text-content-primary" : "text-content-secondary"
            )}
          >
            {tab.label}
          </button>
        ))}
      </div>
      <div
        ref={activeTabRef}
        className="absolute top-2 h-10 rounded-md bg-surface-primary shadow-md transition-all duration-300 ease-out"
      />
    </div>
  );
}

export default SwitchTabs;
