/* eslint-disable react/no-array-index-key */
/* eslint-disable react/require-default-props */
import React, { ReactNode } from "react";
import { twMerge } from "tailwind-merge";
import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import { Pagination } from "swiper/modules";
import styles from "./style.module.scss";

function Slider({ slides, swiperProps }: { slides: ReactNode[]; swiperProps?: SwiperProps }) {
  return (
    <Swiper
      pagination
      modules={[Pagination]}
      {...swiperProps}
      className={twMerge(styles.root, "relative", swiperProps?.className)}
    >
      {slides?.map((item, index) => <SwiperSlide key={index}>{item}</SwiperSlide>)}
    </Swiper>
  );
}

export default Slider;
