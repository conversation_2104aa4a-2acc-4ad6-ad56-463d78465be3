.root {
  overflow: visible;

  :global {
    --swiper-pagination-bottom: 0;
    --swiper-pagination-bullet-inactive-color: rgb(var(--color-surface-thertiary));
    --swiper-pagination-bullet-inactive-opacity: 1;
    --swiper-pagination-color: rgb(var(--color-surface-inverse));

    .swiper {
      // background: red;
      position: relative;
    }

    .swiper-slide {
      transition: scale 0.5s;
    }
    .swiper-slide {
      scale: 0.8;
    }

    .swiper-slide-active {
      scale: 1;
    }

    .swiper-pagination {
      pointer-events: none;
      position: absolute;
      left: 16px;
      bottom: 10px !important;
      top: unset !important;
      text-align: left;
    }
  }
}
