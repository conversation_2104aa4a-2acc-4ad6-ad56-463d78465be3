/* eslint-disable @next/next/no-img-element */
import Slider from "@/components/ui/slider";
import type { <PERSON>a, StoryObj } from "@storybook/react";

const meta: Meta<typeof Slider> = {
  component: Slider,
  title: "Components/ui/Slider"
};

export default meta;
type Story = StoryObj<typeof Slider>;

// 👇 Throws a type error it the args don't match the component props
export const Default: Story = {
  args: {
    slides: [
      <div className="h-40 w-full overflow-hidden rounded-md">
        <img alt="" src="https://picsum.photos/400/200" className="h-full w-full object-cover" />
      </div>,
      <div className="h-40 w-full overflow-hidden rounded-md">
        <img alt="" src="https://picsum.photos/400/200" className="h-full w-full object-cover" />
      </div>,
      <div className="h-40 w-full overflow-hidden rounded-md">
        <img alt="" src="https://picsum.photos/400/200" className="h-full w-full object-cover" />
      </div>,
      <div className="h-40 w-full overflow-hidden rounded-md">
        <img alt="" src="https://picsum.photos/400/200" className="h-full w-full object-cover" />
      </div>,
      <div className="h-40 w-full overflow-hidden rounded-md">
        <img alt="" src="https://picsum.photos/400/200" className="h-full w-full object-cover" />
      </div>
    ],
    swiperProps: {
      slidesPerView: 1.2,
      spaceBetween: -20,
      centeredSlides: true,
      centeredSlidesBounds: true,
      loop: true
    }
  }
};
