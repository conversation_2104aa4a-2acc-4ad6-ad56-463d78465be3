import InputWrapper from "@/components/ui/inputWrapper";
import type { Meta, StoryObj } from "@storybook/react";
import Input from "@/components/ui/input";

const meta: Meta<typeof InputWrapper> = {
  component: InputWrapper,
  title: "Components/ui/InputWrapper"
};

export default meta;
type Story = StoryObj<typeof InputWrapper>;

// 👇 Throws a type error it the args don't match the component props
export const Default: Story = {
  args: {
    children: <Input placeholder="جستجو ..." />
  }
};

export const WithAdornments: Story = {
  args: {
    children: <Input placeholder="جستجو ..." />,
    startAdornment: <div className="bg-black text-white">Start</div>,
    endAdornment: <div className="bg-black text-white">End</div>
  }
};
