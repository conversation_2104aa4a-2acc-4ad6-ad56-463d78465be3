/* eslint-disable react/require-default-props */
import React, { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

export type TInputWrapper = {
  className?: string;
  error?: string;
  isAbsolute?: boolean;
  children?: ReactNode;
  startAdornment?: ReactNode;
  endAdornment?: ReactNode;
  variant?: "outline" | "filled";
  rounded?: "md" | "lg";
};

const bindVariant = {
  filled: "outline outline-1 outline-transparent !bg-gray-40",
  outline:
    "outline outline-1 outline-border-primary hover:outline-2 hover:outline-border-secondary focus-within:outline-2 focus-within:outline-border-secondary"
};

const bindRounded = {
  md: "rounded-md",
  lg: "rounded-full"
};

function InputWrapper({
  className = undefined,
  children,
  error,
  startAdornment,
  endAdornment,
  isAbsolute = false,
  variant = "outline",
  rounded = "md"
}: TInputWrapper) {
  return (
    <>
      <div
        className={twMerge(
          "relative flex items-center gap-2 p-0", // Removed px-3 py-3.5
          bindVariant[variant],
          bindRounded[rounded],
          className
        )}
      >
        {startAdornment && <div className="px-3">{startAdornment}</div>}
        {children}
        {endAdornment && <div className="px-3">{endAdornment}</div>}
        {error && isAbsolute && (
          <p className={twMerge("rtl absolute -bottom-5 right-0 text-xs text-content-on-error-2")}>{error}</p>
        )}
      </div>
      {error && !isAbsolute && <p className={twMerge("rtl mt-1 text-xs text-content-on-error-2")}>{error}</p>}
    </>
  );
}

export default InputWrapper;
