import Trash from "@/assets/icons/mini-trash.svg";
import Minimize from "@/assets/icons/Minimize.svg";
import Plus from "@/assets/icons/plus.svg";
import { twMerge } from "tailwind-merge";

interface ICartModificationProps {
  cartCount: number;
  className?: string;
  onPlus: (value: number) => void;
  onMinimize: (value: number) => void;
}

function CartModification({ cartCount, onMinimize, onPlus, className }: ICartModificationProps) {
  return (
    <div
      className={twMerge("flex h-9 items-center gap-2 rounded-lg border border-border-primary px-2 py-1", className)}
      onClick={e => {
        e.stopPropagation();
        e.preventDefault();
      }}
    >
      <Plus
        onClick={(e: any) => {
          e.stopPropagation();
          e.preventDefault();

          onPlus(cartCount + 1);
        }}
        className="size-6 cursor-pointer"
      />
      <div className="flex size-6 items-center justify-center">
        <span className="text-base font-bold">{cartCount}</span>
      </div>

      <button
        type="button"
        onClick={e => {
          e.stopPropagation();
          e.preventDefault();

          onMinimize(cartCount - 1);
        }}
        className="flex size-6 items-center justify-center text-gray-500 hover:text-red-500"
      >
        {cartCount === 1 ? <Trash className="cursor-pointer" /> : <Minimize className="cursor-pointer" />}
      </button>
    </div>
  );
}

export default CartModification;
