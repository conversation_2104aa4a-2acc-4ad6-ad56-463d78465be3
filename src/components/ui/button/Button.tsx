/* eslint-disable react/require-default-props */
import React, { ReactNode } from "react";
import { twMerge } from "tailwind-merge";
import Loading from "../loading";
import { ButtonSizes, ButtonTheme, ButtonVariant } from "./types";
import { bindButtonSizes, filledVariant, outlineVariant, textVariant } from "./utils";

export interface TButton
  extends React.DetailedHTMLProps<React.ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement> {
  children: React.ReactNode;
  variant?: ButtonVariant;
  theme?: ButtonTheme;
  size?: ButtonSizes;
  isLoading?: boolean;
  disabled?: boolean;
  startAdornment?: ReactNode;
  endAdornment?: ReactNode;
}

function Button({
  children,
  className,
  variant = "filled",
  theme = "primary",
  size = "md",
  disabled,
  isLoading,
  startAdornment,
  endAdornment,
  ...restProps
}: TButton) {
  return (
    // eslint-disable-next-line react/button-has-type
    <button
      {...restProps}
      className={twMerge(
        "flex items-center justify-center gap-2",
        bindButtonSizes[size as ButtonSizes],
        variant === "filled" && filledVariant[theme as ButtonTheme],
        variant === "outline" && outlineVariant[theme as ButtonTheme],
        variant === "text" && textVariant[theme as ButtonTheme],
        disabled ? "cursor-not-allowed transition" : "transition",
        className
      )}
      disabled={disabled}
    >
      {isLoading && <Loading size="xs" />}
      {startAdornment}
      {children}
      {endAdornment}
    </button>
  );
}

export default Button;
