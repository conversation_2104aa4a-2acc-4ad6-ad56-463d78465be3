import Button from "@/components/ui/button";
import type { <PERSON>a, StoryObj } from "@storybook/react";

const meta: Meta<typeof Button> = {
  component: Button,
  title: "Components/ui/Button"
};

export default meta;
type Story = StoryObj<typeof Button>;

// 👇 Throws a type error it the args don't match the component props
export const Default: Story = {
  args: {
    children: "دکمه خرید",
    variant: "filled",
    theme: "primary"
  }
};
export const WithAdornments: Story = {
  args: {
    children: "دکمه خرید",
    variant: "filled",
    theme: "primary",
    startAdornment: <div className="bg-black text-white">Start</div>,
    endAdornment: <div className="bg-black text-white">End</div>
  }
};
