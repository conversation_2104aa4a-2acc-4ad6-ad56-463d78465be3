import { TBindButtonBasedOnSize, TBindButtonBasedOnTheme } from "./types";

export const bindButtonSizes: TBindButtonBasedOnSize = {
  lg: "px-4 py-[13px] rounded-lg text-sm font-medium",
  md: "px-4 py-2 rounded-lg text-sm font-medium",
  sm: "px-2 py-2 rounded-lg text-sm font-medium"
};

export const filledVariant: TBindButtonBasedOnTheme = {
  primary: `
  font-medium
  border border-transparent
  bg-brand-primary focus:bg-brand-primary hover:bg-surface-action-hover
  text-global-white
  focus-within:outline-brand-primary/40 
  disabled:bg-surface-action-disable disabled:text-content-on-action-disable
  `,
  warning: `
  font-medium
  border border-transparent
  bg-surface-warining-2 focus:bg-surface-warining-2 hover:bg-surface-warining-hover
  text-global-white
  focus-within:outline-surface-warining-2/40 
  disabled:bg-surface-warining-1 disabled:text-content-on-warning-disable`
};

export const outlineVariant: TBindButtonBasedOnTheme = {
  primary: `
  font-medium
  border border-border-primary
  bg-transparent
  text-content-on-action-2 hover:text-content-on-action-hover-2
  focus-within:outline-brand-primary/40 
  disabled:text-content-on-action-disable disabled:border-border-border-primary
  `,
  warning: `
  font-medium
  border border-border-primary
  bg-transparent
  text-content-on-warning-2 hover:text-content-on-warning-2
  focus-within:outline-brand-primary/40 
  disabled:text-content-on-warning-disable disabled:border-border-primary
  `
};

export const textVariant: TBindButtonBasedOnTheme = {
  primary: `
  font-medium
  border border-transparent
  bg-transparent
  text-content-on-action-2 hover:text-content-on-action-hover-2
  focus-within:outline-brand-primary/40 
  disabled:text-content-on-action-disable
  `,
  warning: `
  font-medium
  border border-transparent
  bg-transparent
  text-content-on-warning-2 hover:text-content-on-warning-2
  focus-within:outline-brand-primary/40 
  disabled:text-content-on-warning-disable
  `
};
