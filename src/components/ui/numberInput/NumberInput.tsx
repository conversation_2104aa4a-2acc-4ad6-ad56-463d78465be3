import * as React from "react";
import { enNumber, isNumberOrNumberStr, toCommas } from "@/utils/helpers";
import { isNaN } from "lodash";
import { TNumberInput } from "./types";
import Input from "../input";

// Helper function to convert Persian digits to English digits
const convertPersianToEnglish = (str: string): string => {
  if (!str) return "";

  const persianDigits = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];
  const englishDigits = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];

  let result = str;
  for (let i = 0; i < 10; i++) {
    const regex = new RegExp(persianDigits[i], "g");
    result = result.replace(regex, englishDigits[i]);
  }

  return result;
};

// Helper function to convert English digits to Persian digits
const convertEnglishToPersian = (str: string): string => {
  if (!str) return "";

  const persianDigits = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];
  const englishDigits = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];

  let result = str;
  for (let i = 0; i < 10; i++) {
    const regex = new RegExp(englishDigits[i], "g");
    result = result.replace(regex, persianDigits[i]);
  }

  return result;
};

// Interface to extend TNumberInput
interface TPersianNumberInput extends TNumberInput {
  usePersianDigits?: boolean;
}

// eslint-disable-next-line react/display-name
const NumberInput = React.forwardRef<HTMLInputElement, TPersianNumberInput>(
  ({ className, onChange, hasCommaSeparator = false, value, usePersianDigits = true, ...props }, ref) => {
    // Convert value to English for internal processing
    const toEnglishValue =
      value !== undefined && value !== null
        ? convertPersianToEnglish(`${value}`.trim().replaceAll(",", ""))
        : undefined;

    const internalValue =
      toEnglishValue !== undefined && isNumberOrNumberStr(toEnglishValue) ? enNumber(toEnglishValue) : undefined;

    // Format with commas if needed (in English digits)
    const commaSeparatedEnValue =
      hasCommaSeparator && internalValue !== undefined && !isNaN(Number.parseFloat(internalValue))
        ? toCommas(Number.parseFloat(internalValue))
        : internalValue;

    // Convert to Persian for display if needed
    const displayValue =
      usePersianDigits && commaSeparatedEnValue !== undefined
        ? convertEnglishToPersian(commaSeparatedEnValue)
        : commaSeparatedEnValue;

    const HandleOnChangeNumberInput: React.ChangeEventHandler<HTMLInputElement> = e => {
      let inputValue = e.target.value;

      // Convert to English for validation and processing
      const englishValue = convertPersianToEnglish(inputValue);
      const val: string = enNumber(englishValue.trim().replaceAll(",", ""));

      if (!isNumberOrNumberStr(val)) {
        // prevent typing other than number
        return;
      }

      if (isNaN(val)) {
        return;
      }

      // Create a copy of the event to modify
      const modifiedEvent = { ...e };
      modifiedEvent.target = { ...e.target, value: val };

      onChange?.(modifiedEvent as React.ChangeEvent<HTMLInputElement>);
    };

    return (
      <Input
        ref={ref}
        {...props}
        className={className}
        type="tel"
        inputMode="numeric"
        dir={props?.dir || "rtl"} // Right-to-left for Persian
        onChange={HandleOnChangeNumberInput}
        value={displayValue}
      />
    );
  }
);

export default NumberInput;
