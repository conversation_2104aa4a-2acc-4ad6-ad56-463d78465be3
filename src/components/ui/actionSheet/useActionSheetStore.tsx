import { CupertinoPane, CupertinoSettings } from "cupertino-pane";
import { ReactNode } from "react";
import { create } from "zustand";

type Options = {
  title?: ReactNode;
  closable?: boolean;
} & CupertinoSettings;

type TUseActionSheetStore = {
  pane: CupertinoPane | null;
  title?: ReactNode;
  closable?: boolean;
  content: ReactNode | null;
  setPane: (pane: CupertinoPane) => void;
  open: (conten: ReactNode, paneOptions?: Options) => void;
  close: () => Promise<boolean> | undefined;
};

// eslint-disable-next-line import/prefer-default-export
export const useActionSheet = create<TUseActionSheetStore>((set, get) => ({
  pane: null,
  title: undefined,
  closable: true,
  content: null,
  setPane: pane => set(() => ({ pane })),
  open: (conten, paneOptions) => {
    if (get().pane) return;

    const pane = new CupertinoPane(
      ".cupertino-pane", // Pane container selector
      {
        backdrop: true,
        parentElement: "body", // Parent container
        ...paneOptions,
        events: {
          ...paneOptions?.events,
          onDidDismiss(e) {
            set({ pane: null, content: null });
            paneOptions?.events?.onDidDismiss?.(e);
          }
        }
      }
    );
    set(() => ({
      content: conten,
      pane,
      title: paneOptions?.title,
      closable: paneOptions?.closable === undefined
    }));
  },
  close: () => get().pane?.destroy({ animate: true })
}));
