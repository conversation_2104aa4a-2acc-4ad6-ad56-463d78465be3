/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import type { Meta, StoryObj } from "@storybook/react";
import { useActionSheet } from "./useActionSheetStore";

function ActionSheetContent() {
  return <div className="p-8">content</div>;
}

function ActionSheet() {
  const { open, close } = useActionSheet();

  const handleOpenActionSheet = () => {
    open(<ActionSheetContent />, { fitHeight: true, events: { onBackdropTap: close } });
  };

  return (
    <div className="cursor-pointer" onClick={handleOpenActionSheet}>
      Open action sheet
    </div>
  );
}

const meta: Meta<typeof ActionSheet> = {
  component: ActionSheet,
  title: "Components/ui/ActionSheet"
};

export default meta;
type Story = StoryObj<typeof ActionSheet>;

// 👇 Throws a type error it the args don't match the component props
export const Default: Story = {
  args: {}
};
