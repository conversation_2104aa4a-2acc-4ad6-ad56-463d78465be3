"use client";

import React, { useEffect } from "react";
import Close from "@/assets/icons/close.svg";
import { CupertinoPane } from "cupertino-pane";
import { useActionSheet } from "./useActionSheetStore";

function ActionSheetProvider() {
  const { content, pane, close, title, closable } = useActionSheet();

  useEffect(() => {
    if (pane !== null) {
      (pane as CupertinoPane)?.calcFitHeight();
    }

    pane?.present({ animate: true });

    return () => {
      pane?.destroy({ animate: true });
    };
  }, [pane]);

  return (
    <div className="cupertino-pane hidden">
      {(title || closable) && (
        <div className="mt-3 flex items-center justify-between px-4 py-2">
          <span className="text-sm font-medium">{title}</span>
          <Close className="cursor-pointer" onClick={close} />
        </div>
      )}
      {content}
    </div>
  );
}

export default ActionSheetProvider;
