import { thousandSeparator } from "@/utils/number";
import { useTranslations } from "next-intl";
import React from "react";
import { twMerge } from "tailwind-merge";

interface PriceDisplayProps {
  originalPrice?: number;
  finalPrice: number;
  discountPercentage?: number;
  currency?: string;
  percentageClassName?: string;
  finalPriceClassName?: string;
  finalPriceWrapperClassName?: string;
}

export default function PriceDisplay({
  originalPrice,
  finalPrice,
  discountPercentage = 0,
  currency = "تومانءءء",
  percentageClassName,
  finalPriceClassName,
  finalPriceWrapperClassName
}: PriceDisplayProps) {
  const t = useTranslations();

  return (
    <div className="flex flex-col items-end">
      {discountPercentage > 0 && originalPrice && (
        <div className={twMerge("flex items-center gap-2", percentageClassName)}>
          <div className="rounded-full bg-red-500 px-2 py-1 text-10 font-medium text-white">
            {discountPercentage} {t("general.percent")}
          </div>

          <span className="text-xs text-content-tertiary line-through">{thousandSeparator(originalPrice)}</span>
        </div>
      )}

      {finalPrice > 0 && (
        <div
          className={twMerge(
            "flex w-full items-center justify-end gap-1 text-left text-lg font-bold",
            finalPriceWrapperClassName
          )}
        >
          <span className={twMerge("ltr text-15 font-semibold", finalPriceClassName)}>
            {thousandSeparator(finalPrice)}
          </span>
          <span className="text-10 text-content-tertiary">{currency}</span>
        </div>
      )}
    </div>
  );
}
