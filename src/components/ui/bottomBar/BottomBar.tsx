import { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

interface IHeaderProps {
  children?: ReactNode;
  className?: string;
}

function BottomBar({ children, className }: IHeaderProps) {
  return (
    <div
      className={twMerge(
        "mt-auto flex shrink-0 items-center justify-between gap-4 bg-surface-primary px-4 py-4",
        className
      )}
    >
      {children}
    </div>
  );
}

export default BottomBar;
