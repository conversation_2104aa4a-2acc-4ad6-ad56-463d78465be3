"use client";

import { useTranslations } from "next-intl";
import { usePathname } from "next/navigation";
import { twMerge } from "tailwind-merge";
import { HOME, PROFILE, SHOPPING_CART } from "@/constants/nextjsRoutes";
import BottomBar from "@/components/ui/bottomBar";
import HomeIcon from "@/assets/icons/home-icon.svg";
import ProfileIcon from "@/assets/icons/profile-icon.svg";
import HomeActiveIcon from "@/assets/icons/home-active-icon.svg";
import CartActiveIcon from "@/assets/icons/cart-active-icon.svg";
import ProfileActiveIcon from "@/assets/icons/profile-active-icon.svg";
import Link from "next/link";
import { STORE_HANDLE_REGEX } from "@/constants/regexs";
import CartBadge from "../cartBadge/cartBadge";

function BottomNavBar() {
  const t = useTranslations();
  const pathname = usePathname();

  const storeMatch = pathname.match(STORE_HANDLE_REGEX)?.[0] || "";
  /* -------------------------- removes store handle -------------------------- */
  const secondPath = pathname.replace(storeMatch, "");
  /* --------------------------- treat empty as "/" --------------------------- */
  const cleanedPath = secondPath === "" ? "/" : secondPath;

  const items = [
    { id: 1, title: t("menu.home"), path: HOME, icon: <HomeIcon />, activeIcon: <HomeActiveIcon /> },
    { id: 2, title: t("menu.cart"), path: SHOPPING_CART, icon: <CartBadge />, activeIcon: <CartBadge isActive /> },
    { id: 3, title: t("menu.profile"), path: PROFILE, icon: <ProfileIcon />, activeIcon: <ProfileActiveIcon /> }
  ];

  return (
    <BottomBar>
      <div className="item-center flex w-full justify-between px-8 xxs:px-4 xs:px-12">
        {items?.map(item => {
          const isActive = item.path === "/" ? cleanedPath === "/" : cleanedPath.includes(item.path);

          return (
            <Link prefetch href={item.path || ""}>
              <div className="flex flex-col items-center justify-center gap-1.5">
                <div className="size-6">{isActive ? item?.activeIcon : item?.icon}</div>
                <p
                  className={twMerge(
                    isActive ? "bg-surface-info text-content-on-info-2" : "text-content-primary",
                    "rounded-full px-2 py-1 !text-10"
                  )}
                >
                  {item.title}
                </p>
              </div>
            </Link>
          );
        })}
      </div>
    </BottomBar>
  );
}

export default BottomNavBar;
