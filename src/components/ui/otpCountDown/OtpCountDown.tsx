/* eslint-disable no-nested-ternary */
import { useTranslations } from "next-intl";
import React from "react";
import useOtpCountdown from "./useOtpCountdown";
import Loading from "../loading";

interface IOTPCountdownProps {
  initialTime: number;
  onRetry: () => void;
  isLoading?: boolean;
}

const OTPCountdown = ({ initialTime, onRetry, isLoading }: IOTPCountdownProps) => {
  const t = useTranslations();
  const { timeLeft, isRetryVisible, handleRetry, formatTime } = useOtpCountdown({
    initialTime,
    onRetry
  });

  return (
    <div className="my-6">
      {isLoading ? (
        <div className="flex items-center justify-center">
          <Loading size="sm" />
        </div>
      ) : isRetryVisible ? (
        <div onClick={handleRetry} className="h-5">
          <p className="cursor-pointer text-center text-13 text-content-primary">{t("auth.otp.retryCode")}</p>
        </div>
      ) : (
        <p className="flex h-5 items-center justify-center gap-1 text-center text-13 text-content-primary">
          <span>{formatTime(timeLeft)}</span>
          <span className="text-content-tertiary">{t("auth.otp.prefixTime")}</span>
        </p>
      )}
    </div>
  );
};

export default OTPCountdown;
