import { useState, useEffect } from "react";

interface UseOtpCountdownProps {
  initialTime: number;
  onRetry: () => void;
}

const useOtpCountdown = ({ initialTime, onRetry }: UseOtpCountdownProps) => {
  const [timeLeft, setTimeLeft] = useState(initialTime);
  const [isRetryVisible, setIsRetryVisible] = useState(false);

  useEffect(() => {
    setTimeLeft(initialTime);
    setIsRetryVisible(false);
  }, [initialTime]);

  // eslint-disable-next-line consistent-return
  useEffect(() => {
    if (timeLeft > 0) {
      const timerId = setTimeout(() => {
        setTimeLeft(prev => prev - 1);
      }, 1000);
      return () => clearTimeout(timerId);
      // eslint-disable-next-line no-else-return
    } else {
      setIsRetryVisible(true);
    }
  }, [timeLeft]);

  const handleRetry = () => {
    onRetry();
    setTimeout(() => {
      setIsRetryVisible(false);
      setTimeLeft(initialTime);
    }, 0);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
  };

  return {
    timeLeft,
    isRetryVisible,
    handleRetry,
    formatTime
  };
};

export default useOtpCountdown;
