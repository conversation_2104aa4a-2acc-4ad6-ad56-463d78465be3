import Cart from "@/assets/icons/cart-icon.svg";
import { useCartQuery } from "@/services/apis/cart/cart";
import CartActiveIcon from "@/assets/icons/cart-active-icon.svg";
import Loading from "../loading";

function CartBadge({ isActive = false }: { isActive?: boolean }) {
  const { data: carts, isError: isCartError, isFetching } = useCartQuery();

  const totalQuantity = isCartError
    ? 0
    : carts?.data?.line_items?.reduce((prev, current) => prev + (current?.quantity ?? 0), 0);

  return (
    <div className="relative">
      {!!totalQuantity && totalQuantity > 0 && (
        <>
          {isFetching ? (
            <div className="absolute left-full top-[5px] -ml-2 flex w-fit items-center rounded border border-surface-primary bg-content-on-info-2 p-0.5 px-1">
              <Loading size="xs" className="!size-2" />
            </div>
          ) : (
            <div className="absolute left-full top-[5px] -ml-2 flex w-fit items-center rounded border border-surface-primary bg-content-on-info-2 px-1 text-[8px] font-semibold text-surface-primary">
              {totalQuantity}
            </div>
          )}
        </>
      )}
      {isActive ? <CartActiveIcon /> : <Cart />}
    </div>
  );
}

export default CartBadge;
