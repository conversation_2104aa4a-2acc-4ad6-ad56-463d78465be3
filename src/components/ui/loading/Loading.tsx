/* eslint-disable react/require-default-props */
import React from "react";
import { twMerge } from "tailwind-merge";
import styles from "./Loading.module.scss";

const sizes = {
  xs: "h-5 w-5 border-2 border-t-2",
  sm: "h-8 w-8 border-2 border-t-2",
  md: "h-12 w-12",
  lg: "h-16 w-16"
};

function Loading({ className = undefined, size = "md" }: { className?: string; size?: keyof typeof sizes }) {
  return <div className={twMerge(styles.loading, sizes[size], className)} />;
}

export default Loading;
