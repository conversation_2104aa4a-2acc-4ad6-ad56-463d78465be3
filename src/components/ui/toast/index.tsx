import ErrorIcon from "@/assets/icons/error-icon.svg";
import SuccessIcon from "@/assets/icons/success-icon.svg";
import InfoIcon from "@/assets/icons/info-circle.svg";
import { toast as ReactToastify, ToastContentProps, ToastOptions } from "react-toastify";
import { HTMLAttributes, ReactNode } from "react";
import { twMerge } from "tailwind-merge";

const bindProgressBarStrokeColor = {
  default: "#2970FF",
  error: "#F04438",
  info: "#2970FF",
  success: "#18B466",
  warning: "#FC8415"
};

const bindProgressBarBgColor = {
  default: "bg-v2-surface-info",
  error: "bg-v2-surface-error",
  info: "bg-v2-surface-info",
  success: "bg-v2-surface-success",
  warning: "bg-v2-surface-warining-1"
};

function customRender<T extends ReactNode>({ closeToast, isPaused, toastProps, data }: ToastContentProps<T>) {
  const strokeDash = 565.48;
  const attributes: HTMLAttributes<SVGCircleElement> = {};
  const type = toastProps?.type;

  // handle controlled progress bar
  // controlled progress bar uses a transition
  if (typeof toastProps.progress === "number") {
    attributes.style = {
      transition: "all .1s linear",
      strokeDashoffset: `${strokeDash - strokeDash * toastProps.progress}px`
    };

    if (toastProps.progress >= 1) {
      attributes.onTransitionEnd = () => {
        closeToast();
      };
    }
  } else if (toastProps?.autoClose) {
    // normal autoclose uses an animation
    // animation inside index.css
    attributes.className = "animate";
    attributes.style = {
      animationDuration: `${toastProps.autoClose}ms`,
      animationPlayState: isPaused ? "paused" : "running"
    };

    attributes.onAnimationEnd = () => {
      closeToast();
    };
  }

  return (
    <div className="flex w-full items-center gap-2.5">
      <div className={twMerge("relative size-9 shrink-0 rounded-full", bindProgressBarBgColor[type])}>
        {type === "error" && (
          <ErrorIcon className="absolute left-1/2 top-1/2 size-5 -translate-x-1/2 -translate-y-1/2 text-[#F04438]" />
        )}
        {type === "warning" && (
          <ErrorIcon className="absolute left-1/2 top-1/2 size-5 -translate-x-1/2 -translate-y-1/2 text-[#FC8415]" />
        )}
        {type === "success" && (
          <SuccessIcon className="absolute left-1/2 top-1/2 size-5 -translate-x-1/2 -translate-y-1/2 text-[#18B466]" />
        )}
        {(type === "info" || type === "default") && (
          <InfoIcon className="absolute left-1/2 top-1/2 size-5 -translate-x-1/2 -translate-y-1/2 text-[#2970FF]" />
        )}

        {!!toastProps?.autoClose && (
          <svg
            width="44"
            height="44"
            viewBox="-25 -25 250 250"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            className="absolute -right-1 -top-1 -rotate-90"
          >
            <circle
              r="90"
              cx="100"
              cy="100"
              fill="transparent"
              stroke="transparent"
              strokeWidth="6"
              strokeDasharray={`${strokeDash}px`}
              strokeDashoffset="0"
            />
            <circle
              r="90"
              cx="100"
              cy="100"
              stroke={bindProgressBarStrokeColor[type]}
              strokeWidth="10px"
              strokeLinecap="round"
              fill="transparent"
              strokeDasharray={`${strokeDash}px`}
              {...attributes}
            />
          </svg>
        )}
      </div>

      <p className="text-v2-content-primary font-iranYekan text-right text-base font-normal">{data}</p>
    </div>
  );
}

export const toast = <T extends ReactNode>(data: T, options?: ToastOptions<T>) => {
  ReactToastify<T>(customRender, {
    customProgressBar: true,
    data,
    rtl: true,
    icon: false,
    ...options
  });
};
