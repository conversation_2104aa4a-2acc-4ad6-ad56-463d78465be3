import Input from "@/components/ui/input";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

const meta: Meta<typeof Input> = {
  component: Input,
  title: "Components/ui/Input"
};

export default meta;
type Story = StoryObj<typeof Input>;

// 👇 Throws a type error it the args don't match the component props
export const Default: Story = {
  args: {
    placeholder: "جستجو ..."
  }
};
