/* eslint-disable react/require-default-props */
import React from "react";
import { twMerge } from "tailwind-merge";
import styles from "./Input.module.scss";

export type TInput = React.DetailedHTMLProps<React.InputHTMLAttributes<HTMLInputElement>, HTMLInputElement> & {
  className?: string;
};

const Input = React.forwardRef<HTMLInputElement, TInput>(({ className = undefined, ...restProps }, ref) => {
  return (
    <input
      ref={ref}
      className={twMerge("w-full bg-transparent px-3 py-3.5 text-sm leading-5 outline-none", styles.root, className)}
      {...restProps}
    />
  );
});

Input.displayName = "Input";

export default Input;
