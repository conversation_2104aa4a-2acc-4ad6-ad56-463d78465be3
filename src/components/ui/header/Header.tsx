import { ReactNode } from "react";

function Header({
  title,
  startAdornment,
  endAdornment
}: {
  title: string;
  startAdornment?: ReactNode;
  endAdornment?: ReactNode;
}) {
  return (
    <div className="flex h-14 items-center justify-between gap-4 bg-surface-primary p-4">
      <div>{startAdornment}</div>
      <div className="text-sm">{title}</div>
      <div>{endAdornment}</div>
    </div>
  );
}

export default Header;
