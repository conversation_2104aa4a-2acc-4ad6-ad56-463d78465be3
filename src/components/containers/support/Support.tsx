"use client";

import Header from "@/components/ui/header";
import { PROFILE } from "@/constants/nextjsRoutes";
import Link from "next/link";
import ArrowRight from "@/assets/icons/arrow-right.svg";
import SquareArrow from "@/assets/icons/profile-arrow.svg";
import Clock from "@/assets/icons/support-clock.svg";
import Email from "@/assets/icons/support-email.svg";
import Phone from "@/assets/icons/support-phone.svg";
import Telegram from "@/assets/icons/support-telegram.svg";
import DirectInstagram from "@/assets/icons/support-instagram.svg";
import WhatsApp from "@/assets/icons/support-whatsapp.svg";
import Instagram from "@/assets/icons/profile-instagram.svg";
import Redirect from "@/assets/icons/redirect.svg";

import { useTranslations } from "next-intl";
import { useStoreQuery } from "@/services/apis/home/<USER>";
import Loading from "@/components/ui/loading";
import { handleRedirectSocialMedia } from "@/utils/helpers";

function Support() {
  const t = useTranslations();

  const { data: store, isLoading: isLoadingStore } = useStoreQuery();

  return (
    <div className="flex h-full flex-col">
      <Header
        title={t("support.title")}
        startAdornment={
          <Link prefetch href={PROFILE} className="size-6">
            <ArrowRight />
          </Link>
        }
        endAdornment={<div className="size-6" />}
      />

      {isLoadingStore ? (
        <div className="flex h-full w-full items-center justify-center">
          <Loading />
        </div>
      ) : (
        <div className="flex w-full flex-1 flex-col gap-3 overflow-auto p-4">
          <div className="flex items-center justify-center gap-2 rounded-lg bg-surface-primary p-4">
            <Clock />
            <span className="text-xs font-medium">{t("support.notifTime")}</span>
          </div>

          <div className="flex w-full gap-3">
            <div className="flex flex-1 flex-col gap-3 rounded-lg bg-surface-primary p-4">
              <Phone />
              <span className="text-10 font-medium text-content-secondary">{t("support.phoneNumber")}</span>
              <span className="text-13 font-semibold">{store?.data?.contact_number}</span>
            </div>

            <div className="flex flex-1 flex-col gap-3 rounded-lg bg-surface-primary p-4">
              <Email />
              <span className="text-10 font-medium text-content-secondary">{t("support.email")}</span>
              <span className="text-13 font-semibold">{store?.data?.contact_email}</span>
            </div>
          </div>

          {!!store?.data?.instagram_id && (
            <div
              className="flex cursor-pointer items-center justify-between rounded-lg bg-instagram px-3 py-4"
              onClick={() => handleRedirectSocialMedia({ platform: "instagram", sid: store?.data?.instagram_id })}
            >
              <div className="flex items-center gap-2">
                <div>
                  <Instagram className="h-9 w-9" />
                </div>
                <div className="flex flex-col gap-1">
                  <h3 className="text-sm font-semibold text-content-on-action-hover-1">{t("home.instagram.title")}</h3>

                  <span className="text-xs text-content-disable">{store?.data?.instagram_id}</span>
                </div>
              </div>

              <div>
                <SquareArrow />
              </div>
            </div>
          )}

          {(!!store?.data?.instagram_id || !!store?.data?.telegram_id || !!store?.data?.whatsapp_number) && (
            <div>
              <p className="text-xs font-medium text-content-secondary">{t("support.answerInSocialMedia")}</p>

              <div className="mt-3 flex flex-col rounded-lg bg-surface-primary">
                {!!store?.data?.instagram_id && (
                  <div
                    className="flex cursor-pointer items-center justify-between border-b border-b-border-primary-lightest px-3 py-4"
                    onClick={() => handleRedirectSocialMedia({ platform: "instagram", sid: store?.data?.instagram_id })}
                  >
                    <div className="flex items-center gap-2">
                      <DirectInstagram />
                      <span className="text-sm font-medium text-content-primary">{t("support.instagramDirect")}</span>
                    </div>
                    <Redirect />
                  </div>
                )}

                {!!store?.data?.telegram_id && (
                  <div
                    className="flex cursor-pointer items-center justify-between border-b border-b-border-primary-lightest px-3 py-4"
                    onClick={() => handleRedirectSocialMedia({ platform: "telegram", sid: store?.data?.telegram_id })}
                  >
                    <div className="flex items-center gap-2">
                      <Telegram />
                      <span className="text-sm font-medium text-content-primary">{t("support.telegram")}</span>
                    </div>
                    <Redirect />
                  </div>
                )}

                {!!store?.data?.whatsapp_number && (
                  <div
                    className="flex cursor-pointer items-center justify-between px-3 py-4"
                    onClick={() =>
                      handleRedirectSocialMedia({ platform: "whatsapp", sid: store?.data?.whatsapp_number })
                    }
                  >
                    <div className="flex items-center gap-2">
                      <WhatsApp />
                      <span className="text-sm font-medium text-content-primary">{t("support.whatsapp")}</span>
                    </div>
                    <Redirect />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default Support;
