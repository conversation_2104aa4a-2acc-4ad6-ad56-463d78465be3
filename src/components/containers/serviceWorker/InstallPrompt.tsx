"use client";

import React, { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import Button from "@/components/ui/button";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import PWAIcon from "@/assets/icons/pwa.svg";
import DownloadIcon from "@/assets/icons/download.svg";
import InstallPromptInstruction from "./InstallPromptInstruction";

function PwaInstallPrompt() {
  const t = useTranslations();
  const { open, close } = useActionSheet();

  const [isMounted, setIsMounted] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false); // Don't show install button if already installed

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    setIsIOS(/iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream);

    setIsStandalone(window.matchMedia("(display-mode: standalone)").matches);
  }, []);

  if (!isMounted || isStandalone || (process.env.NODE_ENV !== "development" && !isIOS)) {
    return null
  }

  return (
    <div className="bg-smGradient flex w-full items-center justify-between rounded-lg px-4 py-3 h-[65px]">
      <div className="flex flex-col gap-2">
        <PWAIcon />
        <span className="text-sm font-semibold">{t("home.pwaTitle")}</span>
      </div>
      <Button
        variant="filled"
        className="text-content-on-info bg-surface-primary hover:bg-surface-primary focus:bg-surface-primary"
        startAdornment={<DownloadIcon className="text-content-on-info" />}
        onClick={() => {
          open(<InstallPromptInstruction />, {
            bottomClose: true,
            fitHeight: true,
            events: {
              onDidDismiss: () => close(),
              onBackdropTap: () => close()
            }
          });
        }}
      >
        {t("home.installPwa")}
      </Button>
    </div>
  );
}

export default PwaInstallPrompt;
