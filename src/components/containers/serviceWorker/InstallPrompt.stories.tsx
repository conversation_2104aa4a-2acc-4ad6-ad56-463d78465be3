import InstallPrompt from "@/components/containers/serviceWorker/InstallPrompt";
import type { Meta, StoryObj } from "@storybook/react";
import Input from "@/components/ui/input";

const meta: Meta<typeof InstallPrompt> = {
  component: InstallPrompt,
  title: "Components/ui/InstallPrompt"
};

export default meta;
type Story = StoryObj<typeof InstallPrompt>;

// 👇 Throws a type error it the args don't match the component props
export const Default: Story = {
  args: {
    children: <Input placeholder="جستجو ..." />
  }
};

export const WithAdornments: Story = {
  args: {
    children: <Input placeholder="جستجو ..." />,
    startAdornment: <div className="bg-black text-white">Start</div>,
    endAdornment: <div className="bg-black text-white">End</div>
  }
};
