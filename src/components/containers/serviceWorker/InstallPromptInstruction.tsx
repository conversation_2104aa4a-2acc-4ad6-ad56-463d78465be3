"use client";

import React from "react";
import Pwa1Icon from "@/assets/illustrations/pwa1.svg";
import Pwa2Icon from "@/assets/illustrations/pwa2.svg";
import { useTranslations } from "next-intl";
import Button from "@/components/ui/button";
import AddSquareIcon from "@/assets/icons/add-square.svg";
import ShareIos1Icon from "@/assets/icons/share-ios-1.svg";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";

function InstallPromptInstruction() {
  const t = useTranslations("installPrompt");
  const { close } = useActionSheet();

  return (
    <div className="flex flex-col items-center justify-center gap-6 pb-8 pt-14">
      <div className="relative shrink-0">
        <Pwa1Icon />
        <Pwa2Icon className="absolute left-1/2 top-1/2 z-10 -translate-x-1/2 -translate-y-1/2 transform" />
      </div>

      <div className="flex shrink-0 flex-col items-center justify-center gap-11">
        <div className="text-center text-xl font-bold text-content-primary">{t("helpPageTitle")}</div>
        <div className="flex flex-col gap-6">
          <div className="flex items-center justify-start gap-2">
            <div className="flex h-[26px] w-[26px] items-center justify-center overflow-hidden rounded-full bg-surface-action-disable">
              <ShareIos1Icon />
            </div>
            <div className="text-xs font-medium text-content-primary">{t("helpDesc1")}</div>
          </div>
          <div className="flex items-center justify-start gap-2">
            <div className="flex h-[26px] w-[26px] items-center justify-center overflow-hidden rounded-full bg-surface-action-disable">
              <AddSquareIcon />
            </div>
            <div className="text-xs font-medium text-content-primary">{t("helpDesc2")}</div>
          </div>
          <div className="flex items-center justify-start gap-2">
            <div className="flex h-[26px] w-[26px] items-center justify-center overflow-hidden rounded-full bg-surface-action-disable">
              <div className="text-[9px] font-medium text-content-on-action-2">Add</div>
            </div>
            <div className="text-xs font-medium text-content-primary">{t("helpDesc3")}</div>
          </div>
        </div>
        <Button className="w-full" onClick={close} size="lg">
          {t("ok")}
        </Button>
      </div>
    </div>
  );
}

export default InstallPromptInstruction;
