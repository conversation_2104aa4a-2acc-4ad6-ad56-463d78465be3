"use client";

import Header from "@/components/ui/header";
import { PROFILE } from "@/constants/nextjsRoutes";
import Link from "next/link";
import ArrowRight from "@/assets/icons/arrow-right.svg";
import NoAddressIcon from "@/assets/icons/no-address-icon.svg";
import { useTranslations } from "next-intl";
import Loading from "@/components/ui/loading";
import Location from "@/assets/icons/location.svg";
import ArrowLeft from "@/assets/icons/arrow-left.svg";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import AddressForm from "@/app/shipping/(components)/AddressForm";
import { useProfileQuery } from "@/services/apis/profile/profile";
import ShippingInfo from "@/app/shipping/(components)/shippingInfo";

function Address() {
  const t = useTranslations();

  const { data: profile, isLoading: isLoadingProfile } = useProfileQuery();

  const { close, open } = useActionSheet();

  const handleAddAddress = () => {
    open(<AddressForm />, {
      title: t("shipping.addressForm.addTitle"),
      fitHeight: true,
      bottomClose: true,
      backdropBlur: true,
      events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
    });
  };

  return (
    <div className="flex h-full flex-col">
      <Header
        title={t("profile.addressTitle")}
        startAdornment={
          <Link prefetch href={PROFILE} className="size-6">
            <ArrowRight />
          </Link>
        }
        endAdornment={<div className="size-6" />}
      />
      {isLoadingProfile ? (
        <div className="flex h-full w-full items-center justify-center">
          <Loading />
        </div>
      ) : (
        <div className="flex w-full flex-1 flex-col gap-3 overflow-auto p-4">
          <div
            className="flex cursor-pointer items-center justify-between rounded-lg bg-surface-primary p-4"
            onClick={handleAddAddress}
          >
            <div className="flex items-center gap-2">
              <Location />
              <span className="text-xs font-medium">{t("shipping.addNewAddress")}</span>
            </div>
            <ArrowLeft />
          </div>

          {!profile?.data?.addresses?.length ? (
            <div className="flex h-full flex-1 flex-col items-center justify-center gap-4">
              <NoAddressIcon />
              <span className="text-sm font-medium">{t("profile.noAddress")}</span>
            </div>
          ) : (
            <div className="flex flex-col gap-3 rounded-lg bg-surface-primary p-4">
              {profile?.data?.addresses?.map(item => (
                <ShippingInfo address={item} defaultAddress={profile?.data?.default_address} hasEdit isProfile />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default Address;
