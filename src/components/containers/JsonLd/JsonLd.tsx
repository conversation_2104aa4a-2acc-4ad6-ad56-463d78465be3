"use client";

import { PRODUCT } from "@/constants/nextjsRoutes";
import { useProductQuery } from "@/services/apis/product/product";
import { useParams, usePathname } from "next/navigation";

export function JsonLd() {
  const pathname = usePathname();
  const params = useParams();
  const id = params?.id as string;

  const productUrl = typeof window !== "undefined" ? window.location?.href : "";
  const { data: product } = useProductQuery({
    id,
    enabled: !!pathname.includes(PRODUCT) && !!id
  });

  if (pathname.includes(PRODUCT) && id) {
    // Create the JSON-LD object with actual data values
    const jsonLdData = {
      "@context": "https://schema.org/",
      "@type": "Product",
      name: product?.data?.name || "",
      image: product?.data?.cover?.url || "",
      description: product?.data?.description?.substring(0, 160) || "",
      sku: product?.data?.cheapest_variant?.sku || "",
      offers: {
        "@type": "Offer",
        url: productUrl,
        priceCurrency: "IRT",
        price: product?.data?.cheapest_variant?.price?.toString() || "0",
        itemCondition: "https://schema.org/NewCondition",
        availability: "https://schema.org/InStock"
      }
    };

    return (
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(jsonLdData)
        }}
      />
    );
  }
  return null;
}
