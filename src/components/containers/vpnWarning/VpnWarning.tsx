import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import VpnWarningIcon from "@/assets/icons/vpn-warning.svg";
import Button from "@/components/ui/button";
import { useTranslations } from "next-intl";

function VpnWarning() {
  const t = useTranslations();
  const { close } = useActionSheet();

  return (
    <div className="flex flex-col items-center px-4 py-5">
      <VpnWarningIcon />
      <div className="flex flex-col gap-6">
        <p className="text-center text-13 font-medium text-content-primary">{t("vpnWarning.title")}</p>
        <p className="text-center text-13 font-medium text-content-primary">{t("vpnWarning.subtitle")}</p>

        <Button onClick={close} className="mt-6 w-full" size="lg">
          {t("vpnWarning.ok")}
        </Button>
      </div>
    </div>
  );
}

export default VpnWarning;
