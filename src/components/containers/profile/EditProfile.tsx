import { useNameMutation, useProfileQuery } from "@/services/apis/profile/profile";
import { useTranslations } from "next-intl";
import ProfileIcon from "@/assets/icons/profile-big-icon.svg";
import Button from "@/components/ui/button";
import Input from "@/components/ui/input";
import InputWrapper from "@/components/ui/inputWrapper";
import { Controller, useForm } from "react-hook-form";
import * as yup from "yup";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import { yupResolver } from "@hookform/resolvers/yup";
import { handleErrorResponse } from "@/utils/handleError";

function EditProfile() {
  const t = useTranslations();
  const { close } = useActionSheet();

  const { mutate: profileMutate, isPending: isLoadingName } = useNameMutation();
  const { data: profile } = useProfileQuery();

  const schema = yup.object().shape({ name: yup.string().required(t("general.errors.requiredField")) });

  const {
    control,
    handleSubmit,
    setError,
    formState: { errors, isSubmitting }
  } = useForm({ resolver: yupResolver(schema), mode: "all", defaultValues: { name: profile?.data?.name || "" } });

  const onSubmit = async ({ name }: { name: string }) => {
    try {
      await profileMutate(
        { name },
        {
          onSuccess: () => {
            close();
          },
          onError(error) {
            const bodyError = error?.response?.data;
            if (bodyError?.error_detail) {
              handleErrorResponse({ error, bodyError, setHookFormFieldError: setError as any, t });
            } else {
              handleErrorResponse({ error, t });
            }
          }
        }
      );
    } catch (error: any) {
      handleErrorResponse({ error, t });
    }
  };

  return (
    <div className="flex h-full w-full flex-col items-center justify-center p-4 pb-5">
      <ProfileIcon width={65} height={65} />
      <p className="mt-3 text-base font-semibold">{profile?.data?.name || t("profile.unknown")}</p>
      <p className="mt-2 text-13 font-medium text-content-secondary">{profile?.data?.user?.phone_number}</p>

      {/* <div className="mt-3 flex items-center gap-2.5 rounded-full bg-surface-info py-3 px-4 text-content-on-info text-13 font-medium">
        <SmallEditIcon />
        <span>{t("profile.changeProfile")}</span>
      </div> */}

      <form onSubmit={handleSubmit(onSubmit)} className="mt-4 w-full">
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <div className="flex flex-col gap-1">
              <span className="text-13 font-medium text-content-tertiary">{t("profile.name")}</span>
              <InputWrapper variant="filled" className="w-full" isAbsolute error={errors.name?.message}>
                <Input
                  value={field.value}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  placeholder={t("profile.name")}
                  autoComplete="off"
                  aria-invalid={errors.name ? "true" : "false"}
                />
              </InputWrapper>
            </div>
          )}
        />

        <Button
          type="submit"
          className="mt-8 w-full"
          size="lg"
          disabled={isSubmitting || isLoadingName}
          isLoading={isLoadingName || isSubmitting}
        >
          {t("profile.editProfileName")}
        </Button>
      </form>
    </div>
  );
}

export default EditProfile;
