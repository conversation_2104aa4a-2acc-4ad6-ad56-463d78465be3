"use client";

import { useParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { useOrderQuery } from "@/services/apis/order/order";
import Loading from "@/components/ui/loading";
import { dateConverter } from "@/utils/date";
import NoOrder from "@/assets/icons/no-order.svg";
import { thousandSeparator } from "@/utils/number";
import { TOMAN_CURRENCY } from "@/constants/constacts";
import Header from "@/components/ui/header";
import Link from "next/link";
import { ORDER } from "@/constants/nextjsRoutes";
import ArrowRight from "@/assets/icons/arrow-right.svg";
import Image from "next/image";
import OrderCode from "@/assets/icons/order-code-icon.svg";
import OrderStatusIcon from "@/assets/icons/order-status-icon.svg";
import TrackingCode from "@/assets/icons/order-tracking-code-icon.svg";
import PaymentStatus from "@/assets/icons/order-payment-icon.svg";
import Price from "@/assets/icons/order-price-icon.svg";
import OrderDate from "@/assets/icons/order-date-icon.svg";
import OrderCount from "@/assets/icons/order-count-icon.svg";
import Location from "@/assets/icons/order-location-icon.svg";
import OrderStatus from "./OrderStatus";

const Order = () => {
  const params = useParams();
  const id = params?.id as string;
  const t = useTranslations();

  const { data: order, isLoading: isLoadingOrder } = useOrderQuery({ id });

  const totalQuantities = order?.data?.line_items?.reduce(
    (accumulator, currentValue) => accumulator + (currentValue?.quantity || 0),
    0
  );

  return (
    <div className="flex h-full flex-col">
      <Header
        title={t("order.orderDetailTitle")}
        startAdornment={
          <Link prefetch href={ORDER} className="size-6">
            <ArrowRight />
          </Link>
        }
        endAdornment={<div className="size-6" />}
      />

      {isLoadingOrder ? (
        <div className="flex h-full w-full items-center justify-center">
          <Loading />
        </div>
      ) : !order?.data?.id ? (
        <div className="flex h-full flex-col items-center justify-center">
          <NoOrder />
          <p className="mt-4 text-15 font-bold">{t("order.noOrderTitle")}</p>
          <p className="mt-2 text-xs font-medium text-content-secondary">{t("order.noOrderSubtitle")}</p>
        </div>
      ) : (
        <div className="flex flex-1 flex-col gap-3 overflow-auto p-4">
          <div className="flex flex-col rounded-lg bg-surface-primary">
            <div className="flex cursor-pointer items-center justify-between border-b border-b-border-primary-lightest p-3.5">
              <div className="flex items-center gap-2">
                <OrderCode />
                <span className="text-13 font-medium text-content-primary">{t("order.orderCode")}</span>
              </div>
              <span className="text-13 font-medium">{order?.data?.number}</span>
            </div>

            {order?.data?.created_at && (
              <div className="flex cursor-pointer items-center justify-between border-b border-b-border-primary-lightest p-3.5">
                <div className="flex items-center gap-2">
                  <OrderDate />
                  <span className="text-13 font-medium text-content-primary">{t("order.orderDate")}</span>
                </div>
                <span className="text-13 font-medium">
                  {dateConverter(order?.data?.created_at, { format: "YYYY" })}/
                  {dateConverter(order?.data?.created_at, { format: "MM" })}/
                  {dateConverter(order?.data?.created_at, { format: "DD" })}
                </span>
              </div>
            )}
            {!!totalQuantities && totalQuantities > 0 && (
              <div className="flex cursor-pointer items-center justify-between p-3.5">
                <div className="flex items-center gap-2">
                  <OrderCount />

                  <span className="text-13 font-medium">{t("order.count")}</span>
                </div>
                <span className="text-13 font-medium">
                  {totalQuantities} {t("order.number")}
                </span>
              </div>
            )}
          </div>

          <div className="flex flex-col rounded-lg bg-surface-primary p-3">
            <div className="flex w-full flex-col gap-3">
              <div className="flex items-center gap-2">
                <Location />
                <p className="text-sm font-medium">
                  {order?.data?.shipping_address?.state} , {order?.data?.shipping_address?.city}
                </p>
              </div>
              <p className="text-13">{order?.data?.shipping_address?.address1}</p>

              <div className="flex items-center justify-between">
                <span className="text-xs text-content-tertiary">{t("shipping.name")}</span>
                <span className="text-xs font-medium">
                  {order?.data?.shipping_address?.first_name || ""} {order?.data?.shipping_address?.last_name}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-xs text-content-tertiary">{t("shipping.phoneNumber")}</span>
                <span className="text-xs font-medium">{order?.data?.shipping_address?.phone_number}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-content-tertiary">{t("shipping.zipCode")}</span>
                <span className="text-xs font-medium">{order?.data?.shipping_address?.zip}</span>
              </div>
            </div>
          </div>

          <div className="flex flex-col rounded-lg bg-surface-primary">
            <div className="flex cursor-pointer items-center justify-between border-b border-b-border-primary-lightest p-3.5">
              <div className="flex items-center gap-2">
                <OrderStatusIcon />
                <span className="text-sm font-medium text-content-primary">{t("order.orderStatus")}</span>
              </div>

              {!!order?.data?.state && (
                <OrderStatus
                  id={order?.data?.state}
                  title={t(`order.orderStateItems.${order?.data?.state?.toLowerCase()}` as any)}
                />
              )}
            </div>

            {!!order?.data?.transaction?.tracking_code && (
              <div className="flex cursor-pointer items-center justify-between border-b border-b-border-primary-lightest p-3.5">
                <div className="flex items-center gap-2">
                  <TrackingCode />
                  <span className="text-sm font-medium text-content-primary">{t("order.tracingCode")}</span>
                </div>

                <span>{order?.data?.transaction?.tracking_code}</span>
              </div>
            )}

            <div className="flex cursor-pointer items-center justify-between border-b border-b-border-primary-lightest p-3.5">
              <div className="flex items-center gap-2">
                <PaymentStatus />
                <span className="text-sm font-medium text-content-primary">{t("order.paymentStatus")}</span>
              </div>

              {!!order?.data?.payment_state && (
                <OrderStatus
                  id={order?.data?.payment_state}
                  title={t(`order.orderStateItems.${order?.data?.payment_state?.toLowerCase()}` as any)}
                />
              )}
            </div>

            <div className="flex cursor-pointer items-center justify-between border-b border-b-border-primary-lightest p-3.5">
              <div className="flex items-center gap-2">
                <Price />
                <span className="text-sm font-medium text-content-primary">{t("order.price")}</span>
              </div>

              <span className="flex items-center gap-1 text-13 font-medium">
                {thousandSeparator(order?.data?.total_price)}
                <span className="text-10 text-content-tertiary">{TOMAN_CURRENCY}</span>
              </span>
            </div>
          </div>

          {!!order?.data?.line_items?.length && (
            <div className="flex flex-col rounded-lg bg-surface-primary p-4">
              <p className="border-b border-b-border-secondary pb-3 text-xs font-medium text-content-tertiary">
                {t("order.orders")}
              </p>

              <div className="mt-3 flex flex-col gap-3">
                {order?.data?.line_items?.map(lItem => (
                  <div
                    key={lItem?.variant_id}
                    className="flex gap-3 border-b border-b-border-secondary pb-3 last:border-b-transparent"
                  >
                    {lItem?.cover?.url && (
                      <Image
                        key={lItem?.variant_id}
                        src={lItem?.cover?.url}
                        alt={lItem?.cover?.alt}
                        width={72}
                        className="size-[72px] rounded"
                        height={72}
                      />
                    )}

                    <div className="flex flex-col justify-between gap-3">
                      <span className="text-xs font-medium leading-[18px]">{lItem?.name}</span>

                      {lItem?.compare_at_price && lItem?.price > 0 && (
                        <span className="flex items-center gap-1 text-13 font-medium">
                          {thousandSeparator(lItem?.price)}
                          <span className="text-10 text-content-tertiary">{TOMAN_CURRENCY}</span>
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Order;
