import Pending from "@/assets/icons/hugeicons--loading-03.svg";
import Danger from "@/assets/icons/solar--danger-outline.svg";
import BillCheck from "@/assets/icons/solar--bill-check-outline.svg";
import CheckCircle from "@/assets/icons/solar--check-circle-outline.svg";
import Delivery from "@/assets/icons/solar--delivery-outline.svg";
import Revote from "@/assets/icons/solar--revote-outline.svg";
import Forbidden from "@/assets/icons/solar--forbidden-circle-outline.svg";
import Confetti from "@/assets/icons/solar--confetti-outline.svg";
import Chat from "@/assets/icons/solar--chat-square-arrow-outline.svg";
import Close from "@/assets/icons/iconamoon--close-light.svg";

export const orderStatusVariants = {
  Pending: {
    bgColor: "#FFEBD8",
    color: "#D26500",
    icon: <Pending className="text-[#D26500]" />
  },
  PaymentPending: {
    bgColor: "#FFEBD8",
    color: "#D26500",
    icon: <Pending className="text-[#D26500]" />
  },
  PaymentFailed: {
    bgColor: "#FEF3F2",
    color: "#F04438",
    icon: <Danger className="text-[#F04438]" />
  },
  Authorized: {
    bgColor: "#EDFDF4",
    color: "#18B466",
    icon: <BillCheck className="text-[#18B466]" />
  },
  Captured: {
    bgColor: "#EDFDF4",
    color: "#18B466",
    icon: <BillCheck className="text-[#18B466]" />
  },
  InProgress: {
    bgColor: "#FFEBD8",
    color: "#D26500",
    icon: <Pending className="text-[#D26500]" />
  },
  ShippedPartially: {
    bgColor: "#FFEBD8",
    color: "#D26500",
    icon: <Pending className="text-[#D26500]" />
  },
  Paid: {
    bgColor: "#EDFDF4",
    color: "#18B466",
    icon: <BillCheck className="text-[#18B466]" />
  },
  Done: {
    bgColor: "#EDFDF4",
    color: "#18B466",
    icon: <CheckCircle className="text-[#18B466]" />
  },
  Shipped: {
    bgColor: "#F2F6FF",
    color: "#2970FF",
    icon: <Delivery className="text-[#2970FF]" />
  },
  Refunded: {
    bgColor: "#EDFDF4",
    color: "#18B466",
    icon: <Revote className="text-[#18B466]" />
  },
  RefundedPartially: {
    bgColor: "#FFEBD8",
    color: "#D26500",
    icon: <Revote className="text-[#D26500]" />
  },
  Cancelled: {
    bgColor: "#FEF3F2",
    color: "#F04438",
    icon: <Close className="text-[#F04438]" />
  },
  Voided: {
    bgColor: "#FEF3F2",
    color: "#F04438",
    icon: <Forbidden className="text-[#F04438]" />
  },
  Failed: {
    bgColor: "#FEF3F2",
    color: "#F04438",
    icon: <Danger className="text-[#F04438]" />
  },
  Fulfilled: {
    bgColor: "#F7F1FD",
    color: "#604FDC",
    icon: <Confetti className="text-[#604FDC]" />
  },
  ReturnRequested: {
    bgColor: "#FFEBD8",
    color: "#D26500",
    icon: <Chat className="text-[#D26500]" />
  },
  ReturnApproved: {
    bgColor: "#EDFDF4",
    color: "#18B466",
    icon: <Revote className="text-[#18B466]" />
  },
  ReturnRejected: {
    bgColor: "#FEF3F2",
    color: "#F04438",
    icon: <Forbidden className="text-[#F04438]" />
  },
  Returned: {
    bgColor: "#F3F4F6",
    color: "#4D5761",
    icon: <Chat className="text-[#4D5761]" />
  }
};
