import { ReactNode } from "react";
import { orderStatusVariants } from "./utils";

export interface IOrderStatusItems {
  title: string;
  id: keyof typeof orderStatusVariants;
}

interface IOrderStatusVariant {
  color: string;
  bgColor: string;
  icon: ReactNode;
}

interface IOrderStatusProps extends IOrderStatusItems {}

function OrderStatus({ title, id }: IOrderStatusProps) {
  const variant = orderStatusVariants[id] as IOrderStatusVariant;

  return (
    <div className="box-content inline-block">
      <div
        className="flex flex-row items-center gap-2 rounded px-1.5 py-1"
        style={{
          backgroundColor: variant?.bgColor || ""
        }}
      >
        {!!variant?.icon && variant?.icon}
        <span
          className="whitespace-nowrap text-10 font-medium"
          style={{
            color: variant?.color || ""
          }}
        >
          {title}
        </span>
      </div>
    </div>
  );
}

export default OrderStatus;
