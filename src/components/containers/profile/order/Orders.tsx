"use client";

import Loading from "@/components/ui/loading";
import { ORDER, PROFILE } from "@/constants/nextjsRoutes";
import { useOrdersInfiniteQuery } from "@/services/apis/order/order";
import ArrowLeft from "@/assets/icons/mini-arrow.svg";
import Image from "next/image";
import Link from "next/link";
import { InView } from "react-intersection-observer";
import { thousandSeparator } from "@/utils/number";
import { TOMAN_CURRENCY } from "@/constants/constacts";
import { useTranslations } from "next-intl";
import { dateConverter } from "@/utils/date";
import Header from "@/components/ui/header";
import ArrowRight from "@/assets/icons/arrow-right.svg";
import Seprator from "@/assets/icons/seprator.svg";
import NoOrder from "@/assets/icons/no-order.svg";
import OrderStatus from "./OrderStatus";

function Orders() {
  const t = useTranslations();
  const { data: orders, isLoading, isFetching, hasNextPage, fetchNextPage } = useOrdersInfiniteQuery({ page_size: 50 });

  return (
    <div className="flex h-full flex-col">
      <Header
        title={t("order.title")}
        startAdornment={
          <Link prefetch href={PROFILE} className="size-6">
            <ArrowRight />
          </Link>
        }
        endAdornment={<div className="size-6" />}
      />

      <div className="flex flex-1 flex-col gap-3 overflow-auto p-4">
        {isLoading ? (
          <div className="flex h-full w-full items-center justify-center">
            <Loading />
          </div>
        ) : !orders?.pages[0]?.data?.length ? (
          <div className="flex h-full flex-col items-center justify-center">
            <NoOrder />
            <p className="mt-4 text-15 font-bold">{t("order.noOrderTitle")}</p>
            <p className="mt-2 text-xs font-medium text-content-secondary">{t("order.noOrderSubtitle")}</p>
          </div>
        ) : (
          orders?.pages?.map(pageItems =>
            pageItems?.data?.map(item => {
              const totalQuantities = item?.line_items?.reduce(
                (accumulator, currentValue) => accumulator + (currentValue?.quantity || 0),
                0
              );

              const lineItemImageCount = item?.line_items?.filter(i => i?.cover?.url)?.length;

              return (
                <Link prefetch className="rounded-lg bg-surface-primary p-4" href={`${ORDER}/${item.id}`}>
                  <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-0.5">
                      <span className="text-xs font-medium text-content-tertiary">{t("order.orderCode")} : </span>
                      <span className="text-xs font-medium">{item?.number}</span>
                    </div>

                    {!!item?.state && (
                      <OrderStatus
                        id={item?.state}
                        title={t(`order.orderStateItems.${item?.state?.toLowerCase()}` as any)}
                      />
                    )}
                  </div>

                  <div className="mt-3 flex items-center justify-between gap-4 border-b border-b-border-secondary pb-3">
                    <div className="flex items-center gap-2">
                      {totalQuantities && (
                        <>
                          <span className="text-10 text-content-secondary">
                            {totalQuantities} {t("order.product")}
                          </span>
                          <Seprator />
                        </>
                      )}
                      <span className="text-10 text-content-secondary">
                        {dateConverter(item?.created_at, { format: "HH:mm" })} -{" "}
                        {dateConverter(item?.created_at, { format: "YYYY" })}/
                        {dateConverter(item?.created_at, { format: "MM" })}/
                        {dateConverter(item?.created_at, { format: "DD" })}
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium">{t("order.total")} : </span>
                      <span className="flex items-center gap-1 text-xs font-medium">
                        {" "}
                        {thousandSeparator(item?.total_price)}
                        <span className="text-10 text-content-tertiary">{TOMAN_CURRENCY}</span>
                      </span>
                    </div>
                  </div>

                  <div className="mt-3 flex items-center justify-between gap-2">
                    <div className="flex items-center gap-1">
                      {item?.line_items?.length &&
                        item?.line_items?.map((lItem, index) => (
                          <>
                            {!!lItem?.cover?.url && (
                              <div className="relative flex items-center justify-center overflow-hidden rounded border border-border-secondary p-1">
                                <div>
                                  <Image
                                    key={lItem?.variant_id}
                                    src={lItem?.cover?.url}
                                    alt={lItem?.cover?.alt}
                                    width={26}
                                    className="size-[26px] rounded"
                                    height={26}
                                  />
                                </div>
                                {!!lineItemImageCount &&
                                  item.line_items.length - 1 === index &&
                                  lineItemImageCount >= 3 && (
                                    <div
                                      style={{ background: "rgba(0, 0, 0, 0.4)" }}
                                      className="text-body4-medium absolute left-0 top-0 hidden h-full w-full items-center justify-center last:flex"
                                    >
                                      <span className="text-xs font-medium text-content-on-action-hover-1">
                                        {lineItemImageCount}+
                                      </span>
                                    </div>
                                  )}
                              </div>
                            )}
                          </>
                        ))}
                    </div>
                    <Link prefetch href={`${ORDER}/${item.id}`} className="flex items-center gap-1">
                      <span className="text-xs font-medium text-content-on-info">{t("order.showOrderDetail")}</span>
                      <ArrowLeft />
                    </Link>
                  </div>
                </Link>
              );
            })
          )
        )}
      </div>
      {hasNextPage && !isFetching && <InView as="div" onChange={inView => (inView ? fetchNextPage() : null)} />}
      {isFetching && (
        <div className="flex items-center justify-center py-4">
          <Loading size="xs" />
        </div>
      )}
    </div>
  );
}
export default Orders;
