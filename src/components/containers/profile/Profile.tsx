"use client";

import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import { getCookie } from "@/hooks/useCookie";
import { ACCESS_TOKEN } from "@/constants/cookies";
import ProfileEmpty from "@/assets/icons/profile-empty.svg";
import Button from "@/components/ui/button";
import { useTranslations } from "next-intl";
import BottomNavBar from "@/components/ui/bottomNavBar";
import { useProfileQuery } from "@/services/apis/profile/profile";
import Loading from "@/components/ui/loading";
import ProfileIcon from "@/assets/icons/profile-icon2.svg";
import EditIcon from "@/assets/icons/edit-02.svg";
import Link from "next/link";
import { ADDRESS, ORDER, Support, WISH_LIST } from "@/constants/nextjsRoutes";
import OrderIcon from "@/assets/icons/order-profile-icon.svg";
import LocationIcon from "@/assets/icons/location-profile-icon.svg";
import FavoriteIcon from "@/assets/icons/favourite-profile-icon.svg";
import SupportIcon from "@/assets/icons/support-profile-icon.svg";
import LogoutIcon from "@/assets/icons/logout-profile-icon.svg";
import ArrowLeft from "@/assets/icons/arrow-left.svg";
import EditProfile from "./EditProfile";
import Logout from "./Logout";
import Auth from "../auth/Auth";

function Profile() {
  const t = useTranslations();
  const accessToken = getCookie(ACCESS_TOKEN);
  const { open, close } = useActionSheet();

  const { data: profile, isLoading: isLoadingProfile } = useProfileQuery();

  const onOpenLogin = () => {
    open(
      <div>
        <Auth />
      </div>,
      {
        closable: false,
        fitHeight: true,
        bottomClose: true,
        backdropBlur: true,
        events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
      }
    );
  };

  const onChangeName = () => {
    open(<EditProfile />, {
      title: t("profile.editProfile"),
      fitHeight: true,
      bottomClose: true,
      backdropBlur: true,
      events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
    });
  };

  const onLogout = () => {
    open(<Logout />, {
      closable: false,
      fitHeight: true,
      bottomClose: true,
      backdropBlur: true,
      events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
    });
  };

  return (
    <div className="flex h-full flex-col">
      <div className="flex items-center justify-center bg-surface-primary p-4">
        <span className="text-sm">{t("profile.title")}</span>
      </div>

      {isLoadingProfile ? (
        <div className="flex h-full w-full items-center justify-center">
          <Loading />
        </div>
      ) : !accessToken ? (
        <div className="flex flex-1 flex-col items-center justify-center overflow-auto">
          <div className="flex flex-col items-center justify-center gap-3">
            <ProfileEmpty />
            <span className="text-13 font-medium">{t("profile.noAccount")}</span>
            <Button onClick={onOpenLogin} size="lg">
              {t("profile.login")}
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex flex-1 flex-col gap-3 overflow-auto p-4">
          <div className="flex items-center justify-between rounded-lg bg-surface-primary px-3.5 py-3">
            <div className="flex items-center gap-3">
              <ProfileIcon />

              <div className="flex flex-col gap-2">
                <span className="text-sm font-medium">{profile?.data?.name || t("profile.unknown")}</span>
                <span className="text-xs text-content-secondary">{profile?.data?.user?.phone_number}</span>
              </div>
            </div>

            <div className="rounded-full bg-surface-info p-[10px]" onClick={onChangeName}>
              <EditIcon className="size-4 cursor-pointer" />
            </div>
          </div>

          <div className="flex flex-col rounded-lg bg-surface-primary">
            <Link
              prefetch
              href={ORDER}
              className="flex items-center justify-between border-b border-b-border-primary-lightest px-3 py-4"
            >
              <div className="flex items-center gap-2">
                <OrderIcon />
                <span className="text-sm font-medium text-content-primary">{t("profile.myOrder")}</span>
              </div>
              <ArrowLeft />
            </Link>

            <Link
              prefetch
              href={ADDRESS}
              className="flex items-center justify-between border-b border-b-border-primary-lightest px-3 py-4"
            >
              <div className="flex items-center gap-2">
                <LocationIcon />
                <span className="text-sm font-medium text-content-primary">{t("profile.myAddress")}</span>
              </div>
              <ArrowLeft />
            </Link>

            <Link
              prefetch
              href={WISH_LIST}
              className="flex items-center justify-between border-b border-b-border-primary-lightest px-3 py-4"
            >
              <div className="flex items-center gap-2">
                <FavoriteIcon />
                <span className="text-sm font-medium text-content-primary">{t("profile.myList")}</span>
              </div>
              <ArrowLeft />
            </Link>

            <Link href={Support} className="flex items-center justify-between px-3 py-4">
              <div className="flex items-center gap-2">
                <SupportIcon />
                <span className="text-sm font-medium text-content-primary">{t("profile.support")}</span>
              </div>
              <ArrowLeft />
            </Link>
          </div>

          <div className="flex cursor-pointer flex-col rounded-lg bg-surface-primary px-3.5 py-4" onClick={onLogout}>
            <div className="flex items-center gap-2">
              <LogoutIcon />
              <span className="text-sm font-medium text-content-on-error-2">{t("profile.logout")}</span>
            </div>
          </div>
        </div>
      )}

      <BottomNavBar />
    </div>
  );
}
export default Profile;
