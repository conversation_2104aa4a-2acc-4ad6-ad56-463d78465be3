"use client";

import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import Button from "@/components/ui/button";
import { logout } from "@/utils/logout";
import { useTranslations } from "next-intl";
import LogoutIcon from "@/assets/icons/logout-icon.svg";
import { HOME } from "@/constants/nextjsRoutes";

function Logout() {
  const t = useTranslations();
  const { close } = useActionSheet();

  const onLogout = () => {
    logout();
    close();
    setTimeout(() => {
      if (typeof window !== "undefined") window.location.assign(HOME);
    }, 0);
  };

  return (
    <div className="flex w-full flex-col items-center justify-center px-4 pb-5 pt-6">
      <LogoutIcon />
      <p className="mt-5 text-base font-bold">{t("profile.logoutTitle")}</p>
      <p className="mt-2 text-xs font-medium text-content-tertiary">{t("profile.logoutSubtitle")}</p>

      <div className="mt-6 flex w-full flex-col gap-4">
        <Button size="lg" className="w-full bg-content-on-error-2" onClick={onLogout}>
          {t("profile.confirm")}
        </Button>
        <Button size="lg" variant="outline" className="w-full text-content-secondary" onClick={close}>
          {t("profile.cancel")}
        </Button>
      </div>
    </div>
  );
}

export default Logout;
