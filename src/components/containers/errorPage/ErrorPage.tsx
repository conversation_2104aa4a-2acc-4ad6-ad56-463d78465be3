import Button from "@/components/ui/button";
import { HOME } from "@/constants/nextjsRoutes";
import { useTranslations } from "next-intl";
import Image from "next/image";
import React from "react";
import ChevronLeft from "@/assets/icons/chevron-left.svg";

function ErrorPage() {
  const t = useTranslations();
  return (
    <div dir="rtl" className="bg-cards flex h-screen w-full items-center justify-center">
      <div className="flex flex-col items-center">
        <Image src="/illustrations/somethingwentwrong.png" alt="somethingwentwrong" width={200} height={200} />

        <h5 className="text-h5-bold text-gray-999 mt-8">{t("somethingwentrong.title")}</h5>
        <p className="text-body3-medium mt-2 text-gray-600">{t("somethingwentrong.subtitle")}</p>
        <Button
          className="mt-4"
          onClick={() => {
            window.location.assign(HOME);
          }}
          endAdornment={<ChevronLeft className="size-5 !text-[white]" />}
        >
          {t("somethingwentrong.button")}
        </Button>
      </div>
    </div>
  );
}

export default ErrorPage;
