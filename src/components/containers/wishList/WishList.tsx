"use client";

import ArrowRight from "@/assets/icons/arrow-right.svg";
import Header from "@/components/ui/header";
import Loading from "@/components/ui/loading";
import { PROFILE } from "@/constants/nextjsRoutes";

import { useWishListQuery } from "@/services/apis/profile/profile";
import { useTranslations } from "next-intl";
import Link from "next/link";
import WishCard from "./WishCard";

function WishList() {
  const t = useTranslations();
  const { data: wishList, isLoading: isLoadingWishList } = useWishListQuery();

  return (
    <div className="flex h-full w-full flex-col">
      <Header
        title={t("wishList.title")}
        startAdornment={
          <Link prefetch href={PROFILE} className="size-6">
            <ArrowRight />
          </Link>
        }
        endAdornment={<div className="size-6" />}
      />

      {isLoadingWishList ? (
        <div className="flex w-full flex-1 items-center justify-center">
          <Loading />
        </div>
      ) : !wishList?.data?.length ? (
        <div className="flex w-full flex-1 items-center justify-center">
          <span className="text-sm text-content-secondary">{t("wishList.noList")}</span>
        </div>
      ) : (
        <div className="p- flex flex-1 flex-col gap-3 overflow-auto p-4">
          {wishList?.data?.map(item => <WishCard key={item?.id} item={item} />)}
        </div>
      )}
    </div>
  );
}

export default WishList;
