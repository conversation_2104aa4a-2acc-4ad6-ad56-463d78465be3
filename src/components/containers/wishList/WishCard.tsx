import { calculateDiscount } from "@/app/(home)/(components)/utils";
import Loading from "@/components/ui/loading";
import PriceDisplay from "@/components/ui/priceDisplay/PriceDisplay";
import { useDeleteWishlistMutation } from "@/services/apis/profile/profile";
import { IWishListData } from "@/services/apis/profile/types";
import { handleErrorResponse } from "@/utils/handleError";
import RemoveFavoriteIcon from "@/assets/icons/remove-favorite-icon.svg";
import Image from "next/image";
import { useTranslations } from "next-intl";

interface IWishCardProps {
  item: IWishListData;
}

function WishCard({ item }: IWishCardProps) {
  const t = useTranslations();

  const { mutate: removeWishListMutate, isPending: isLoadingDeleteWishList } = useDeleteWishlistMutation();

  const onRemoveWishList = async (id: string) => {
    try {
      removeWishListMutate(
        { product_id: id },
        {
          onSuccess: () => {},
          onError(error) {
            if (error) {
              handleErrorResponse({ error: error as any, t });
            }
          }
        }
      );
    } catch (error: any) {
      handleErrorResponse({ error, t });
    }
  };

  return (
    <div className="flex justify-between gap-3 rounded-lg bg-surface-primary px-2 py-2.5">
      <div className="flex flex-1 flex-col justify-between gap-4">
        <p className="text-13 font-medium leading-6">{item?.name}</p>

        <div className="flex items-center justify-between gap-4">
          <PriceDisplay
            finalPrice={item?.cheapest_variant?.price ?? 0}
            discountPercentage={
              calculateDiscount(item?.cheapest_variant?.price ?? 0, item?.cheapest_variant?.compare_at_price ?? 0) ?? 0
            }
            originalPrice={item?.cheapest_variant?.compare_at_price}
            finalPriceWrapperClassName="text-right justify-start"
          />

          {isLoadingDeleteWishList ? (
            <Loading size="xs" />
          ) : (
            <div className="flex cursor-pointer items-center gap-1" onClick={() => onRemoveWishList(item?.id)}>
              <RemoveFavoriteIcon />
              <span className="text-xs font-medium text-content-secondary">{t("wishList.remove")}</span>
            </div>
          )}
        </div>
      </div>
      <Image
        src={item?.cover?.url}
        alt={item?.cover?.alt}
        width={100}
        height={100}
        className="size-[100px] object-contain"
      />
    </div>
  );
}

export default WishCard;
