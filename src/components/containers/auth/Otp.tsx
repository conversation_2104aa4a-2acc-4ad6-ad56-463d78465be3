import * as yup from "yup";
import OTPInput from "react-otp-input";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import OTPCountdown from "@/components/ui/otpCountDown/OtpCountDown";
import Button from "@/components/ui/button";
import { twMerge } from "tailwind-merge";
import Close from "@/assets/icons/close.svg";
import ArrowRight from "@/assets/icons/arrow-right.svg";
import EditIcon from "@/assets/icons/edit.svg";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import { useAuthMutation, useLoginByOtpMutation } from "@/services/apis/auth/auth";
import { setCookie } from "@/hooks/useCookie";
import { ACCESS_TOKEN, USER_ID } from "@/constants/cookies";
import { handleErrorResponse } from "@/utils/handleError";
import { toast } from "@/components/ui/toast";
import queryString from "query-string";
import { useQueryClient } from "@tanstack/react-query";
import { PROFILE } from "@/constants/apiRoutes";
import NumberInput from "@/components/ui/numberInput";

interface IOtpProps {
  phoneNumber?: string;
  expiresIn?: number;
  setExpiresIn: (value?: number) => void;
  onChangePhoneNumber: (value: string) => void;
  onSuccess?: () => void;
}

const Otp = ({ phoneNumber, onChangePhoneNumber, expiresIn, setExpiresIn, onSuccess }: IOtpProps) => {
  const t = useTranslations();
  // const { getCookie, setCookie } = useCookie();
  const { close } = useActionSheet();
  const queryClient = useQueryClient();

  const { mutate: loginMutate, isPending: isLoadingLogin } = useLoginByOtpMutation();
  const { mutate: authMutate, isPending: isAuthLoading } = useAuthMutation();

  const [otpTime, setOtpTime] = useState(0);

  const validationSchema = yup.object({
    otpCode: yup.string().min(5, t("auth.errors.minOtpCode")).required(t("auth.errors.requiredOtpCode"))
  });

  const {
    handleSubmit,
    setError,
    formState: { errors, touchedFields, isSubmitting },
    setValue,
    watch
  } = useForm({ resolver: yupResolver(validationSchema), defaultValues: { otpCode: "" }, mode: "onSubmit" });

  const otpCode = watch("otpCode");

  const onSubmit = async ({ otpCode: otp }: { otpCode: string }) => {
    try {
      // await setApiUserContextHeader(userType);

      await loginMutate(
        { otp, username: phoneNumber || "" },
        {
          onSuccess: ({ data }) => {
            setCookie(ACCESS_TOKEN, data?.data?.token);
            setCookie(USER_ID, data?.data?.user_id);
            queryClient?.invalidateQueries({ queryKey: [queryString.stringifyUrl({ url: PROFILE })] });

            onSuccess?.();

            setTimeout(() => {
              close();
            }, 0);
          },
          onError: error => {
            if (error?.response?.status === 401) {
              toast(t("serverErrors.fields.otp401"), { type: "error" });
            } else if (error) {
              toast(t("serverErrors.fields.otp401"), { type: "error" });
            }
          }
        }
      );
    } catch (error: any) {
      handleErrorResponse({ error, t });
    }
  };

  const handleRetryOtp = async () => {
    setExpiresIn(undefined);
    try {
      await authMutate(
        { username: phoneNumber || "" },
        {
          onSuccess: ({ data }) => {
            setOtpTime(data?.data?.expires_in);
          },
          onError(error) {
            if (error?.response?.status === 401) {
              const bodyError = { ...error?.response?.data, error_detail: { otpCode: ["auth401"] } };

              handleErrorResponse({ error, bodyError, setHookFormFieldError: setError as any, t });
            } else if (error?.response?.status === 400) {
              const bodyError = { ...error?.response?.data, error_detail: { otpCode: ["phoneNumber"] } };
              handleErrorResponse({ error, bodyError, setHookFormFieldError: setError as any, t });
            } else if (error?.response?.status === 404) {
              const notfoundError = { ...error?.response?.data, error_detail: { otpCode: ["notfound"] } };

              handleErrorResponse({ error, bodyError: notfoundError, setHookFormFieldError: setError as any, t });
            } else if (error) {
              handleErrorResponse({ error, t });
            }
          }
        }
      );
    } catch (error: any) {
      handleErrorResponse({ error, t });
    }
  };

  useEffect(() => {
    if (otpCode?.length === 5) {
      handleSubmit(onSubmit)();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [otpCode]);

  useEffect(() => {
    if (expiresIn) setOtpTime(expiresIn);
  }, [expiresIn]);

  return (
    <div className="px-4 py-5">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <ArrowRight className="cursor-pointer" onClick={() => onChangePhoneNumber("")} />
          <span className="text-sm font-semibold text-content-primary">{t("auth.otpTitle")}</span>
        </div>

        <Close className="cursor-pointer" onClick={close} />
      </div>

      <div className="mt-6 flex items-center justify-between rounded-md bg-surface-info p-2 text-13 text-content-primary">
        <div className="flex items-center gap-0.5">
          <span>{t("auth.otp.sendOtp1")}</span>
          <span className="text-13 font-medium">{phoneNumber}</span>
          <span>{t("auth.otp.sendOtp2")}</span>
        </div>

        <EditIcon className="cursor-pointer" onClick={() => onChangePhoneNumber("")} />
      </div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="relative mt-6 flex flex-col justify-center">
          <OTPInput
            shouldAutoFocus
            numInputs={5}
            containerStyle={{ gap: "20px", justifyContent: "center", direction: "ltr" }}
            onChange={v => {
              setValue("otpCode", v, { shouldTouch: true });
            }}
            value={otpCode}
            renderInput={props => (
              <NumberInput
                {...props}
                autoFocus
                // className="auth-otp-code-input"
                className={twMerge(
                  "border-v2-border-primary text-v2-content-primary placeholder:text-v2-border-primary size-10 rounded-lg border border-transparent bg-surface-thertiary text-center text-2xl font-medium xmd:size-12",
                  touchedFields.otpCode && errors.otpCode && "!border-v2-content-on-action-2"
                )}
                style={{}}
              />
            )}
          />
          {errors.otpCode && (
            <p className="absolute -bottom-6 left-1/2 -translate-x-1/2 whitespace-nowrap text-center text-xs text-content-on-error-2">
              {errors.otpCode ? errors.otpCode.message : " "}
            </p>
          )}
        </div>
        <div className="mt-6">
          <OTPCountdown initialTime={otpTime} onRetry={handleRetryOtp} isLoading={false} />
        </div>
        <Button
          color="primary"
          className="mt-6 w-full"
          type="submit"
          size="lg"
          disabled={isLoadingLogin || isSubmitting || isAuthLoading}
          isLoading={isLoadingLogin || isSubmitting}
        >
          {t("auth.otp.confirm")}
        </Button>

        <p className="mt-4 flex items-center justify-center gap-0.5 text-center text-10">
          <span>{t("auth.otp.hint1")}</span>
          <span className="text-content-on-info">{t("auth.otp.hint2")}</span>
          <span>{t("auth.otp.hint3")}</span>
        </p>
      </form>
    </div>
  );
};

export default Otp;
