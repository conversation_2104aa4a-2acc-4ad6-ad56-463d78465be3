import { useF<PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Button from "@/components/ui/button";
import InputWrapper from "@/components/ui/inputWrapper";
import { useTranslations } from "next-intl";
import Close from "@/assets/icons/close.svg";
import Smartphone from "@/assets/icons/smartphone.svg";

import NumberInput from "@/components/ui/numberInput";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import { useAuthMutation } from "@/services/apis/auth/auth";
import { handleErrorResponse } from "@/utils/handleError";

interface ILoginProps {
  onChangePhoneNumber: (value: string) => void;
  setExpiresIn: (value?: number) => void;
}

function Login({ onChangePhoneNumber, setExpiresIn }: ILoginProps) {
  const t = useTranslations();
  const { close } = useActionSheet();
  const { mutate: authMutate, isPending } = useAuthMutation();

  const schema = yup.object().shape({
    phoneNumber: yup
      .string()
      .required(t("auth.errors.phoneRequired"))
      .matches(/^09\d{9}$/, t("auth.errors.phoneInvalid"))
  });

  const {
    control,
    handleSubmit,
    setError,
    formState: { errors, isSubmitting }
  } = useForm({ resolver: yupResolver(schema), mode: "onBlur", defaultValues: { phoneNumber: "" } });

  const onSubmit = async ({ phoneNumber }: { phoneNumber: string }) => {
    try {
      await authMutate(
        { username: phoneNumber },
        {
          onSuccess: data => {
            if (data?.data?.data?.expires_in) {
              onChangePhoneNumber(phoneNumber);
              setExpiresIn(data?.data?.data?.expires_in);
              // queryClient?.invalidateQueries({ queryKey: [queryString.stringifyUrl({ url: PROFILE })] });
            }
          },
          onError(error) {
            if (error?.response?.status === 401) {
              const bodyError = { ...error?.response?.data, error_detail: { phoneNumber: ["auth401"] } };

              handleErrorResponse({ error, bodyError, setHookFormFieldError: setError as any, t });
            } else if (error?.response?.status === 400) {
              const bodyError = { ...error?.response?.data, error_detail: { phoneNumber: ["phoneNumber"] } };
              handleErrorResponse({ error, bodyError, setHookFormFieldError: setError as any, t });
            } else if (error?.response?.status === 404) {
              const notfoundError = { ...error?.response?.data, error_detail: { phoneNumber: ["notfound"] } };

              handleErrorResponse({ error, bodyError: notfoundError, setHookFormFieldError: setError as any, t });
            } else if (error) {
              handleErrorResponse({ error, t });
            }
          }
        }
      );
    } catch (error: any) {
      handleErrorResponse({ error, t });
    }
  };

  return (
    <div className="flex flex-col items-center px-4 py-5">
      <div className="flex w-full items-center justify-between">
        <div className="flex items-center gap-2">
          <Smartphone />
          <span className="text-sm font-semibold text-content-primary">{t("auth.loginTitle")}</span>
        </div>

        <Close className="cursor-pointer" onClick={close} />
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="mt-6 w-full">
        <Controller
          name="phoneNumber"
          control={control}
          render={({ field }) => (
            <InputWrapper variant="filled" className="ltr w-full" isAbsolute error={errors.phoneNumber?.message}>
              <NumberInput
                value={field.value}
                onChange={field.onChange}
                onBlur={field.onBlur}
                dir="ltr"
                placeholder="09✲✲✲✲✲✲✲✲"
                autoComplete="off"
                aria-invalid={errors.phoneNumber ? "true" : "false"}
              />
            </InputWrapper>
          )}
        />

        <Button
          type="submit"
          className="mt-8 w-full"
          size="lg"
          disabled={isSubmitting || isPending}
          isLoading={isPending || isSubmitting}
        >
          {t("auth.loginButton")}
        </Button>
      </form>

      <p className="mt-4 text-10">
        {t("auth.loginHint1")} <span className="text-content-on-info">{t("auth.loginHint2")}</span>{" "}
        <span>{t("auth.loginHint3")}</span> <span className="text-content-on-info">{t("auth.loginHint4")}</span>{" "}
        <span>{t("auth.loginHint5")}</span>
      </p>
    </div>
  );
}

export default Login;
