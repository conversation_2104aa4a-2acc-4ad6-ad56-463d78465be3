import { useEffect, useState } from "react";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import Login from "./Login";
import Otp from "./Otp";

interface IAuthProps {
  onSuccess?: () => void;
}

function Auth({ onSuccess }: IAuthProps) {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [expiresIn, setExpiresIn] = useState<number | undefined>(undefined);
  const { pane } = useActionSheet();

  const onChangePhoneNumber = (val: string) => {
    setPhoneNumber(val);
  };

  useEffect(() => {
    pane?.calcFitHeight();
  }, [pane, phoneNumber]);

  if (phoneNumber) {
    return (
      <Otp
        phoneNumber={phoneNumber}
        onChangePhoneNumber={onChangePhoneNumber}
        expiresIn={expiresIn}
        setExpiresIn={setExpiresIn}
        onSuccess={onSuccess}
      />
    );
  }

  return (
    <div>
      <Login onChangePhoneNumber={onChangePhoneNumber} setExpiresIn={setExpiresIn} />
    </div>
  );
}

export default Auth;
