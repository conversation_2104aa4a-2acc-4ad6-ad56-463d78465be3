import { PRODUCT, STORE } from "@/constants/apiRoutes";
import { createServerApiService } from "@/services/serverApiService";
import { stripHtmlTags } from "./helpers";

export async function generateProductMetaTags({
  params,
  storeHandle
}: {
  params: { id: string };
  storeHandle?: string;
}) {
  const serverApiService = createServerApiService(storeHandle);

  const product = await serverApiService
    .get(PRODUCT(params.id))
    .then(res => res.data?.data)
    .catch(() => null);

  const store = await serverApiService
    .get(STORE)
    .then(res => res?.data?.data)
    .catch(() => null);

  const title = `${product?.name} | ${store?.name || "Shop Builder"}`;

  const rawDescription = product?.description || "Shop Builder";
  const plainDescription = stripHtmlTags(rawDescription).slice(0, 160);

  return {
    title: title || "Shop Builder",
    description: plainDescription,
    icons: {
      icon: store?.logo || "/icon-192x192.png",
      shortcut: store?.logo || "/icon-192x192.png",
      apple: store?.logo || "/icon-192x192.png"
    },
    themeColor: "#00359e",
    openGraph: {
      title: title || "Shop Builder",
      description: plainDescription,
      type: "website",
      images: [{ url: product?.cover?.url, alt: title }]
    },
    twitter: {
      card: "summary",
      title: title || "Shop Builder",
      description: plainDescription,
      images: [product?.cover?.url]
    },
    appleWebApp: { capable: true }
  };
}
