import { toast } from "@/components/ui/toast";
import { AxiosError } from "axios";

export type ApiError = { error: string; error_detail: { [k: string]: string[] } | Array<string> };

export const handleError = async (error: any, t: any) => {
  const message = error?.message || error?.response?.message || error?.data?.error || error?.data;

  //   if (response?.data && (!response.data.error_detail || response.status !== 400)) {
  //     clientDefaultErrorHandler(response.data);
  //   }

  if (error?.response?.status >= 500) {
    toast(t("globalErrorMessage"), { type: "error" });
    return;
  }

  if (message) {
    const errorMessage = message;
    const serverErrorKey = "serverErrors." + errorMessage;
    const serverErrorTranslation = t(serverErrorKey);

    const translatedMessage =
      serverErrorTranslation === serverErrorKey ? t("globalErrorMessage") : serverErrorTranslation;

    toast(translatedMessage, { type: "error" });
  } else if (error?.response?.data?.error) {
    toast(t("globalErrorMessage"), { type: "error" });
  } else if (message === "Network Error") {
    toast(t("networkerror.subtitle"), { type: "error" });
  }
};

export type SetHookFormError = (
  f: string,
  error: { message: string; type: "validation" | "required" | "custom" }
) => void;

export type ClientDefaultErrorHandler = {
  bodyError?: ApiError;
  error?: AxiosError<ApiError>;
  setHookFormFieldError?: SetHookFormError;
  t: any;
};

export const handleErrorResponse = ({ bodyError, setHookFormFieldError, error, t }: ClientDefaultErrorHandler) => {
  //   const t = createTranslator({ locale: "fa" });

  if (Object.keys(bodyError?.error_detail || {})?.length) {
    Object.entries(bodyError?.error_detail || {}).forEach(([key, value]) => {
      let fieldMessages = "";
      const messageKeys = (value as [string, string])[0].split(/[.|:]/);
      const errorKey = key.split(/[.|:]/)?.[0];

      messageKeys.forEach((messageKey: string) => {
        // some messages are with value like minLength:1
        const hasValue = messageKeys?.[1] || false;
        const trKey = `serverErrors.fields.${hasValue ? messageKey.split(":")[0] : messageKey}`;
        const translation = t(trKey as any, { value: hasValue });

        if (translation !== trKey) {
          if (setHookFormFieldError) {
            fieldMessages += `\n${translation}`;
          } else {
            toast(`${errorKey}: ${translation}`, { type: "error" });
          }
        }
      });

      if (fieldMessages) {
        if (setHookFormFieldError) {
          setHookFormFieldError(errorKey, { message: fieldMessages.trim(), type: "validation" });
        }
      }
    });
  } else {
    handleError(error, t);
  }
};

export const snakeToCamelCaseHookFormWrapper =
  (setHookFormFieldError?: SetHookFormError) =>
  (field: string, error: { message: string; type: "validation" | "required" | "custom" }) => {
    setHookFormFieldError?.(field, error);
  };
