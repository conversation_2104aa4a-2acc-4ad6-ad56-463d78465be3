import { CATEGORIES, STORE } from "@/constants/apiRoutes";
import { createServerApiService } from "@/services/serverApiService";

export interface IGeneralMetaTags {
  storeHandle?: string;
  params: { id: string };
}

export async function generateCategoryMetaTags({ storeHandle, params }: IGeneralMetaTags) {
  const serverApiService = createServerApiService(storeHandle);

  const categories = await serverApiService
    .get(CATEGORIES)
    .then(res => res.data?.data?.find((item: any) => item.id === params?.id)?.name)
    .catch(() => null);

  const categoryName = categories;

  const store = await serverApiService
    .get(STORE)
    .then(res => res?.data?.data)
    .catch(() => null);

  const title = `${categoryName} | ${store?.name || "Shop Builder"}`;

  return {
    title: title,
    description: store?.description || "Shop Builder",
    icons: {
      icon: store?.logo || "/icon-192x192.png",
      shortcut: store?.logo || "/icon-192x192.png",
      apple: store?.logo || "/icon-192x192.png"
    },
    themeColor: "#00359e",
    openGraph: {
      title: title || "Shop Builder",
      description: store?.description || "Shop Builder",
      type: "website",
      images: [{ url: store?.logo, alt: title }]
    },
    twitter: {
      card: "summary",
      title: title || "Shop Builder",
      description: store?.description || "Shop Builder",
      images: [store?.logo]
    },
    appleWebApp: { capable: true }
  };
}
