/* eslint-disable no-nested-ternary */
export const formatDate = (
  date: string | Date,
  settings?: Intl.DateTimeFormatOptions,
  local: Intl.LocalesArgument = "fa-IR"
) => {
  if (typeof date === "string") {
    return new Intl.DateTimeFormat(local, settings).format(new Date(date));
  }
  return new Intl.DateTimeFormat(local, settings).format(date);
};

export function timeAgo(dateString: string): { value?: number; suffix: string } {
  if (!dateString) return { value: undefined, suffix: "" };

  const date = new Date(dateString);
  const now = new Date();
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  let interval = Math.floor(seconds / 31536000); // Years
  if (interval >= 1) return { value: interval, suffix: `year${interval > 1 ? "s" : ""} ago` };

  interval = Math.floor(seconds / 2592000); // Months
  if (interval >= 1) return { value: interval, suffix: `month${interval > 1 ? "s" : ""} ago` };

  interval = Math.floor(seconds / 604800); // Weeks
  if (interval >= 1) return { value: interval, suffix: `week${interval > 1 ? "s" : ""} ago` };

  interval = Math.floor(seconds / 86400); // Days
  if (interval >= 1) return { value: interval, suffix: `day${interval > 1 ? "s" : ""} ago` };

  interval = Math.floor(seconds / 3600); // Hours
  if (interval >= 1) return { value: interval, suffix: `hour${interval > 1 ? "s" : ""} ago` };

  interval = Math.floor(seconds / 60); // Minutes
  if (interval >= 1) return { value: interval, suffix: `minute${interval > 1 ? "s" : ""} ago` };

  return { value: undefined, suffix: "just now" };
}

/**
 * Convert date to any format
 * @example dateConverter("2024-02-06T20:00:00.000Z") // 1402/11/17
 * @example dateConverter(new Date("2024-02-06T20:00:00.000Z")) // 1402/11/17
 * @example dateConverter("2024-02-06T20:00:00.000Z", { format: "YYYY/MM/DD" })
 * @example dateConverter("2024-02-06T20:00:00.000Z", { format: "YYYY/MM/DD", locale: "ar-EG", calendar: "persian"  })
 *
 * @param // config.format:
 * - YYYY: 2024
 * - YY: 24
 * - MMMM: January
 * - MMM: Jan
 * - MM: 01
 * - M: 1
 * - DD: 01
 * - D: 1
 * - HH: 00-23
 * - H: 0-23
 * - hh: 01-12
 * - h: 1-12
 * - mm: 00-59
 * - m: 0-59
 * - ss: 00-59
 * - s: 0-59
 * - A: AM/PM
 * - a: am/pm
 */
export function dateConverter(
  date: Date | string,
  config?: {
    format?: string;
    locale?: string;
    calendar?: string;
  }
) {
  const { format = "YYYY/MM/DD", locale = "fa-IR", calendar = "persian" } = config || {};
  const finalDate = typeof date === "string" ? new Date(date) : date;

  const year = Intl.DateTimeFormat(locale, {
    year: format?.includes("YYYY") ? "numeric" : "2-digit",
    calendar
  })
    .format(new Date(finalDate))
    .replace(/\s.*$/, "");

  const month = Intl.DateTimeFormat(locale, {
    month: format?.includes("MMMM")
      ? "long"
      : format?.includes("MMM")
        ? "short"
        : format?.includes("MM")
          ? "2-digit"
          : "numeric",
    calendar
  }).format(new Date(finalDate));

  const day = Intl.DateTimeFormat(locale, {
    day: format?.includes("DD") ? "2-digit" : "numeric",
    calendar
  }).format(new Date(finalDate));

  let hour24 = Intl.DateTimeFormat(locale, {
    hour: format?.includes("HH") ? "2-digit" : "numeric",
    hour12: false,
    calendar
  }).format(new Date(finalDate));

  if (format?.includes("HH") && hour24.startsWith("0")) {
    hour24 = hour24?.replace(/^0(\d)$/, "$1");
  }

  const hour12 = Intl.DateTimeFormat(locale, {
    hour: format?.includes("hh") ? "2-digit" : "numeric",
    hour12: true,
    calendar
  }).format(new Date(finalDate));

  const minute = Intl.DateTimeFormat(locale, {
    minute: format?.includes("mm") ? "2-digit" : "numeric",
    calendar
  }).format(new Date(finalDate));

  const second = Intl.DateTimeFormat(locale, {
    second: format?.includes("ss") ? "2-digit" : "numeric",
    calendar
  }).format(new Date(finalDate));

  const ampm = hour12
    // replace all numbers and just letters
    .replace(/[0-9\s]/g, "");

  let result = format;
  result = result.replace("YYYY", year);
  result = result.replace("YY", year);
  result = result.replace("MMMM", month);
  result = result.replace("MMM", month);
  result = result.replace("MM", month);
  result = result.replace("M", month);
  result = result.replace("DD", day);
  result = result.replace("D", day);
  result = result.replace("HH", hour24);
  result = result.replace("H", hour24);
  result = result.replace("hh", hour12.replace(/\s.*$/, ""));
  result = result.replace("h", hour12.replace(/\s.*$/, ""));
  result = result.replace("mm", minute);
  result = result.replace("m", minute);
  result = result.replace("ss", second);
  result = result.replace("s", second);
  result = result.replace("A", ampm); // must at the end to prevent conflict with month (M)
  result = result.replace("a", ampm?.toLowerCase()); // must at the end to prevent conflict with minute (m)

  return result;
}
