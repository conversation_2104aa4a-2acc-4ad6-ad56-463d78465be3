export const thousandSeparator = (num?: number | null): string | null => {
  if (!num) return null;

  // Convert the number to a string
  const numStr = num.toString();

  // Split the string into integer and decimal parts (if any)
  const [integerPart, decimalPart] = numStr.split(".");

  // Use a regular expression to add commas as thousand separators
  const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  // Combine the integer part with the decimal part if it exists
  return decimalPart ? `${formattedIntegerPart}.${decimalPart}` : formattedIntegerPart;
};
