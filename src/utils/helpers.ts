export const isNumber = (val: any) => typeof val === "number";

export const isString = (val: any) => typeof val === "string";

export const isNumberString = (val: any) => isString(val) && !Number.isNaN(Number(val));

export function enNumber(value: string | number): string {
  if (!value) return value as string;
  return `${value}`
    .replace(/[٠١٢٣٤٥٦٧٨٩]/gm, v => String.fromCharCode(v.charCodeAt(0) - 1584))
    .replace(/[۰۱۲۳۴۵۶۷۸۹]/gm, v => String.fromCharCode(v.charCodeAt(0) - 1728));
}
export function toCommas(value: number) {
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
export const isNumberOrNumberStr = (val: any) => isNumber(val) || isNumberString(val);

export const handleRedirectSocialMedia = ({
  platform,
  sid
}: {
  platform: "telegram" | "whatsapp" | "instagram";
  sid: string;
}) => {
  switch (platform) {
    case "telegram":
      window.location.href = `https://t.me/${sid}`;
      break;
    case "whatsapp":
      window.location.href = `https://wa.me/${sid}`;
      break;
    case "instagram":
      window.location.href = `https://instagram.com/${sid}`;
      break;
    default:
      break;
  }
};

export function stripHtmlTags(html: string): string {
  return html.replace(/<[^>]+>/g, "").trim();
}
