"use server";

import { STORE_HANDLE_REGEX } from "@/constants/regexs";
import { cookies, headers } from "next/headers";

// export async function getServerCookies(key: string) {
//   const cookieStore = cookies();

//   return cookieStore.get(key)?.value || null;
// }

export async function getServerCookies(key: string) {
  const headersList = await headers();
  const pathname = headersList.get("x-pathname") || headersList.get("x-url");

  if (!pathname) return null;

  const storeRegex = STORE_HANDLE_REGEX;
  const storeHandleMatch = pathname.match(storeRegex)?.[0];
  return storeHandleMatch || null;
}
