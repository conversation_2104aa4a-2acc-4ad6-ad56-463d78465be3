@tailwind base;

/* 
https://tailwindcss.com/docs/customizing-colors#using-css-variables
*/
@layer base {
  :root {
    --color-global-emerald-400: 27 200 149;
    --color-global-orange-500: 252 132 21;
    --color-global-white: 255 255 255;

    --color-background-base-white: 255 255 255;
    --color-background-secondary: 243 244 246;
    --color-brand-primary: 41 112 255;

    --color-gray-20: 249 250 251;
    --color-gray-40: 241 243 246;
    --color-gray-200: 184 187 192;

    --color-surface-primary: 255 255 255;
    --color-surface-info: 242 246 255;
    --color-surface-action-hover: 4 33 133;
    --color-surface-action-light: 247 241 253;
    --color-surface-action-disable: 232 237 255;
    --color-surface-secondary: 249 250 251;
    --color-surface-secondary-light: 27 30 35;
    --color-surface-thertiary: 243 244 246;
    --color-surface-inverse: 18 18 18;
    --color-surface-warining-1: 255 235 216;
    --color-surface-warining-2: 252 142 40;
    --color-surface-warining-hover: 252 132 21;

    --color-border-secondary: 243 244 246;
    --color-border-secondary-light: 38 39 41;
    --color-border-primary: 229 231 235;
    --color-border-primary-lightest: 244 245 246;
    --color-border-tertiary: 244 245 246;

    --color-content-on-action-disable: 148 171 255;
    --color-content-on-action-2: 27 77 255;
    --color-content-on-info: 0 53 158;
    --color-content-on-info-2: 41 112 255;
    --color-content-on-success-2: 24 180 102;
    --color-content-on-action-hover-1: 249 250 251;
    --color-content-on-action-hover-2: 15 60 217;
    --color-content-primary: 26 29 33;
    --color-content-secondary: 114 124 141;
    --color-content-subtle: 185 191 198;
    --color-content-secondary-light: 152 151 151;
    --color-content-tertiary: 108 115 127;
    --color-content-on-warning-disable: 255 199 147;
    --color-content-on-warning-2: 210 101 0;
    --color-content-on-error-2: 240 68 56;
    --color-content-disable: 221 224 228;

    --color-semantic-neutral-400: 136 144 159;
    --color-semantic-neutral-200: 179 184 194;
  }

  html.dark {
    --color-background-base-white: 255 255 255;
    --color-background-secondary: 26 29 33;
    --color-brand-primary: 27 77 255;

    --color-surface-primary: 18 18 18;
    --color-surface-action-hover: 4 33 133;
    --color-surface-action-light: 247 241 253;
    --color-surface-action-disable: 232 237 255;
    --color-surface-secondary: 26 29 33;
    --color-surface-thertiary: 61 67 76;
    --color-surface-inverse: 255 255 255;
    --color-surface-warining-1: 255 235 216;
    --color-surface-warining-2: 252 142 40;
    --color-surface-warining-hover: 252 132 21;

    --color-border-secondary: 27 77 255;
    --color-border-primary: 61 67 76;
    --color-border-primary-lightest: 26 29 33;
    --color-border-tertiary: 26 29 33;

    --color-content-on-action-disable: 148 171 255;
    --color-content-on-action-2: 27 77 255;
    --color-content-on-action-hover-1: 249 250 251;
    --color-content-on-action-hover-2: 15 60 217;
    --color-content-primary: 244 245 246;
    --color-content-secondary: 114 124 141;
    --color-content-tertiary: 157 164 176;
    --color-content-on-warning-disable: 255 199 147;
    --color-content-on-warning-2: 210 101 0;
    --color-content-disable: 221 224 228;

    --color-semantic-neutral-400: 136 144 159;
    --color-semantic-neutral-200: 179 184 194;
  }
}
