import localFont from "next/font/local";

export const IRANYekanXFaNumPro = localFont({
  src: [
    {
      path: "../assets/fonts/woff/IRANYekanXFaNum-Thin.woff",
      weight: "100",
      style: "normal"
    },
    {
      path: "../assets/fonts/woff/IRANYekanXFaNum-UltraLight.woff",
      weight: "200",
      style: "normal"
    },
    {
      path: "../assets/fonts/woff/IRANYekanXFaNum-Light.woff",
      weight: "300",
      style: "normal"
    },
    {
      path: "../assets/fonts/woff/IRANYekanXFaNum-Medium.woff",
      weight: "500",
      style: "normal"
    },
    {
      path: "../assets/fonts/woff/IRANYekanXFaNum-DemiBold.woff",
      weight: "600",
      style: "normal"
    },
    {
      path: "../assets/fonts/woff/IRANYekanXFaNum-ExtraBold.woff",
      weight: "800",
      style: "normal"
    },
    {
      path: "../assets/fonts/woff/IRANYekanXFaNum-Black.woff",
      weight: "900",
      style: "normal"
    },
    {
      path: "../assets/fonts/woff/IRANYekanXFaNum-ExtraBlack.woff",
      weight: "950",
      style: "normal"
    },
    {
      path: "../assets/fonts/woff/IRANYekanXFaNum-Heavy.woff",
      weight: "1000",
      style: "normal"
    },
    {
      path: "../assets/fonts/woff/IRANYekanXFaNum-Bold.woff",
      weight: "bold",
      style: "normal"
    },
    {
      path: "../assets/fonts/woff/IRANYekanXFaNum-Regular.woff",
      weight: "normal",
      style: "normal"
    }
  ],
  variable: "--font-iran-yekan"
});
