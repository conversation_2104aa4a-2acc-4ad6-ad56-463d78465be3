import ActionSheetProvider from "@/components/ui/actionSheet/ActionSheetProvider";
import React from "react";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import NextIntlProvider from "./NextIntlProvider";
import ReactQueryProvider from "./ReactQueryProvider";
import StoreHandle from "./StoreHanlde";

function Providers({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <NextIntlProvider>
      <NuqsAdapter>
        <ReactQueryProvider>
          {children}
          <StoreHandle />
          <ActionSheetProvider />
        </ReactQueryProvider>
      </NuqsAdapter>
    </NextIntlProvider>
  );
}

export default Providers;
