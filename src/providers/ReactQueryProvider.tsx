"use client";

import { getQueryClient } from "@/services/reactQuery";
import { QueryClientProvider } from "@tanstack/react-query";
import React from "react";

function ReactQueryProvider({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [queryClient] = React.useState(() => getQueryClient());

  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
}

export default ReactQueryProvider;
