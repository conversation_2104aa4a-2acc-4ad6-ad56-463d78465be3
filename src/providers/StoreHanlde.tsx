"use client";

import { STORE_HANDLE } from "@/constants/cookies";
import { STORE_HANDLE_REGEX } from "@/constants/regexs";
import { setCookie } from "@/hooks/useCookie";
import { usePathname } from "next/navigation";
import { useEffect, useRef } from "react";

function StoreHandle() {
  const pathname = usePathname();
  const prevStoreHandleRef = useRef<string | null>(null);

  useEffect(() => {
    const storeRegex = STORE_HANDLE_REGEX;

    const storeHandleMatch = pathname.match(storeRegex)?.[0] as string;

    if (storeHandleMatch) {
      /* --------- Update the cookie only if the store handle has changed --------- */
      if (prevStoreHandleRef.current !== storeHandleMatch) {
        setCookie(STORE_HANDLE, storeHandleMatch);
        prevStoreHandleRef.current = storeHandleMatch;
      }
    }
  }, [pathname]);

  return null;
}

export default StoreHandle;
