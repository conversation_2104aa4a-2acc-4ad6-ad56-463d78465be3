import { useState, useEffect } from "react";

interface RecentSearchesConfig {
  key?: string;
  maxItems?: number;
}

interface UseRecentSearchesReturn {
  searches: string[];
  addSearch: (term: string) => void;
  removeSearch: (term: string) => void;
  clearSearches: () => void;
}

function useRecentSearches(config?: RecentSearchesConfig): UseRecentSearchesReturn {
  const { key = "recent-searches", maxItems = 10000 } = config || {};

  // Initialize state with localStorage data
  const [searches, setSearches] = useState<string[]>(() => {
    try {
      const stored = localStorage.getItem(key);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error("Error parsing localStorage data:", error);
      return [];
    }
  });

  // Sync state to localStorage
  useEffect(() => {
    try {
      localStorage.setItem(key, JSON.stringify(searches));
    } catch (error) {
      console.error("Error saving to localStorage:", error);
    }
  }, [key, searches]);

  const addSearch = (term: string): void => {
    const trimmedTerm = term.trim();
    if (!trimmedTerm) return;

    setSearches(prev => {
      const filtered = prev.filter(item => item.toLowerCase() !== trimmedTerm.toLowerCase());
      return [trimmedTerm, ...filtered].slice(0, maxItems);
    });
  };

  const removeSearch = (term: string): void => {
    const trimmedTerm = term.trim();
    if (!trimmedTerm) return;

    setSearches(prev => prev.filter(item => item.toLowerCase() !== trimmedTerm.toLowerCase()));
  };

  const clearSearches = (): void => {
    setSearches([]);
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error("Error clearing localStorage:", error);
    }
  };

  return { searches, addSearch, removeSearch, clearSearches };
}

export default useRecentSearches;
