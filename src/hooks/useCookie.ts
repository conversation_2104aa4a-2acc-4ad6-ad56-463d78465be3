import Cookies from "js-cookie";

// const defaultPath = typeof window !== "undefined" ? window.location.pathname : "/";

export const setCookie = (name: string, value: string) => Cookies.set(name, value);

export const getCookie = (name: string) => Cookies.get(name);

export const removeCookie = (name: string): void => Cookies.remove(name);

const useCookie = () => ({ setCookie, getCookie, removeCookie });

export default useCookie;
