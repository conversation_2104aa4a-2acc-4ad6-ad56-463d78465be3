import { useAddProductMutation, useCartQuery, useRemoveProductMutation } from "@/services/apis/cart/cart";
import { Variant } from "@/services/apis/product/types";
import { handleErrorResponse } from "@/utils/handleError";
import { useTranslations } from "next-intl";

export type TVariant = { variant_id: string } & Variant;

interface ICartModification {
  variants?: Variant[];
  singleVariant?: TVariant;
  showNotification?: () => void;
  onChangeCartCount?: (value: number) => void;
}

export const useCartModification = ({
  variants,
  singleVariant,
  showNotification,
  onChangeCartCount
}: ICartModification) => {
  const variant = (variants?.[0] || singleVariant) as TVariant;
  const t = useTranslations();

  const { data: carts, isLoading: isLoadingCart } = useCartQuery();

  const cart = carts?.data?.line_items?.find(item => item?.variant_id === (variant?.variant_id || variant?.id));

  const { mutate: addProduct, isPending: isPendingAddProduct } = useAddProductMutation();
  const { mutate: removeProduct, isPending: isPendingRemoveProduct } = useRemoveProductMutation();

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleAddProduct = async (_quantity: number) => {
    showNotification?.();

    try {
      addProduct(
        { product_id: variant?.product_id, quantity: "1", variant_id: variant?.variant_id || variant?.id },
        {
          onError(error) {
            onChangeCartCount?.(cart?.quantity ?? 0);
            if (error) {
              handleErrorResponse({ error, t });
            }
          }
        }
      );
    } catch (error: any) {
      handleErrorResponse({ error, t });
    }
  };

  const handleRemoveProduct = async (quantity: number) => {
    if (!!quantity && quantity > 0) {
      showNotification?.();
    }

    try {
      removeProduct(
        {
          product_id: variant?.product_id,
          quantity: quantity > 0 ? "1" : quantity?.toString(),
          variant_id: variant?.variant_id || variant?.id
        },
        {
          onError(error) {
            onChangeCartCount?.(cart?.quantity ?? 0);
            if (error) {
              if (error?.response?.status === 417) return;
              handleErrorResponse({ error, t });
            }
          }
        }
      );
    } catch (error: any) {
      handleErrorResponse({ error, t });
    }
  };

  return { handleRemoveProduct, handleAddProduct, isLoadingCart, isPendingAddProduct, isPendingRemoveProduct };
};
