import { create } from "zustand";
import Cookies from "js-cookie";
import { CART_DATA } from "@/constants/cookies";
import { CartData, CartStore } from "./types";

const useCartStore = create<CartStore>(set => ({
  /* ------------- Initial State (retrieving from a single cookie) ------------ */
  ...JSON.parse(Cookies.get(CART_DATA) || "{}"),

  setCart: (cartData: CartData) => {
    /* ---------------- Merge existing cart data with new data --------------- */
    const currentData: CartData = JSON.parse(Cookies.get(CART_DATA) || "{}");
    const updatedData: CartData = {
      ...currentData,
      ...cartData,
      cartId: cartData?.cartId
    };

    /* ----------------- Set the cart data to a single cookie ---------------- */
    Cookies.set(CART_DATA, JSON.stringify(updatedData));

    // Update Zustand state
    set(updatedData);
  },

  clearCart: () => {
    /* ---------------- Remove the cookie and reset Zustand state --------------- */
    Cookies.remove(CART_DATA);
    set({
      cartId: null
    });
  },

  getCart: (): CartData | null => {
    /* ------------------ Retrieve cart data from the cookie ----------------- */
    const cartData = Cookies.get(CART_DATA);
    return cartData ? JSON.parse(cartData) : null;
  }
}));

export default useCartStore;
