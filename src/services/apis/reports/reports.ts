import queryString from "query-string";
import {
  REPORTS_ANNUAL,
  REPORTS_DAILY,
  REPORTS_MONTHLY,
  REPORTS_QUARTERLY,
  REPORTS_WEEKLY
} from "@/constants/apiRoutes";
import { useQuery } from "@tanstack/react-query";
import { TReportsResponse } from "./reportsTypes";

export type TPriceChartInterval = "daily" | "weekly" | "3_months" | "yearly" | "monthly";

type TGetReportParams = {
  id: string;
  date_interval: TPriceChartInterval;
};

const bindUrl = {
  daily: REPORTS_DAILY,
  weekly: REPORTS_WEEKLY,
  monthly: REPORTS_MONTHLY,
  "3_months": REPORTS_QUARTERLY,
  yearly: REPORTS_ANNUAL
};

export const useReportsQuery = (params: TGetReportParams) =>
  useQuery<TReportsResponse>({
    queryKey: [
      queryString.stringifyUrl({
        url: bindUrl?.[params.date_interval]?.(params.id)
      })
    ],
    staleTime: 30000 // 30 seconds
  });
