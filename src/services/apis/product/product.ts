import queryString from "query-string";
import { PRODUCT, PRODUCTS } from "@/constants/apiRoutes";
import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { apiService } from "@/services/apiService";
import { IGetProductParams, IGetProductsParams, TProductResponse, TProductsResponse } from "./types";

export const useProductsInfiniteQuery = (params: IGetProductsParams) =>
  useInfiniteQuery<TProductsResponse>({
    queryKey: [queryString.stringifyUrl({ url: PRODUCTS(params.category_id) })],
    queryFn: ({ pageParam }) =>
      apiService
        .get(
          queryString.stringifyUrl({
            url: PRODUCTS(params.category_id),
            query: { ...params, page: (pageParam as number) || undefined }
          })
        )
        .then(({ data }) => data),
    enabled: !!params.category_id,
    initialPageParam: 1,
    getNextPageParam: lastPage => {
      const currentPage = lastPage.pagination.page;
      const totalItems = lastPage.pagination.total;
      const pageSize = lastPage.pagination.page_size;
      const totalPages = Math.ceil(totalItems / pageSize);

      if (currentPage >= totalPages) return null;

      return currentPage + 1;
    }
  });

export type TPriceChartInterval = "daily" | "weekly" | "3_months" | "yearly" | "monthly";

export const useProductQuery = (params: IGetProductParams) =>
  useQuery<TProductResponse>({
    queryKey: [queryString.stringifyUrl({ url: PRODUCT(params?.id) })],
    enabled: !!params?.enabled
    // staleTime: 30000 // 30 seconds
  });
