type Category = {
  icon: string;
  id: string;
  image: string;
  is_active: boolean;
  name: string;
  position: number;
  sub_categories: string[];
};

export type Variant = {
  compare_at_price: number;
  created_at: string;
  ean: string;
  id: string;
  is_active: boolean;
  options: Record<string, string>;
  price: number;
  product_id: string;
  stock: number;
  updated_at: string;
  sku?: string;
};

export type Product = {
  category: Category;
  category_id: string;
  cheapest_price: number;
  cheapest_variant: Variant;
  cover: {
    alt: string;
    marked_as_cover: boolean;
    url: string;
  };
  description: string;
  featured: boolean;
  handle: string;
  has_variant: boolean;
  id: string;
  images: {
    alt: string;
    marked_as_cover: boolean;
    url: string;
  }[];
  is_active: boolean;
  name: string;
  properties: { [key: string]: string[] };
  return_policy: {
    description: string;
    is_allowed: boolean;
    shipping_payer: "Customer" | "Seller";
    window_time: {
      max: number;
      min: number;
    };
  };
  shipping_policies: {
    carrier: string;
    class: string;
    created_at: string;
    description: string;
    excluded: boolean;
    extra_item_rate: number;
    location_id: string;
    prepaid: boolean;
    rate: number;
    shipping_time: {
      max: number;
      min: number;
    };
    updated_at: string;
  }[];
  store_id: string;
  tags: string[];
  variants: Variant[];
};

type DataItem = {
  leaf_categories: Category[];
  matched_category: Category;
  matched_root_category: Category;
  products: Product[];
};

export interface TProductsResponse {
  data: DataItem;
  pagination: {
    page: number;
    page_size: number;
    total: number;
  };
}
export interface TProductResponse {
  data: Product;
  status: string;
}

export interface IGetProductParams {
  id: string;
  enabled?: boolean;
  // params?: {
  //   date_interval?: TPriceChartInterval;
  // };
}

export interface IGetProductsParams {
  // name?: string | null;
  // date_interval?: "daily";
  category_id: string;
  page_size?: number;
  page?: number;
}
