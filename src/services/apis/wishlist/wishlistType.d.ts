interface TWishlistResponse {
  data: Datum[];
  links: Links;
  meta: Meta;
}

interface Meta {
  current_page: number;
  from: number;
  last_page: number;
  links: Link[];
  path: string;
  per_page: number;
  to: number;
  total: number;
}

interface Link {
  url: null | string;
  label: string;
  active: boolean;
}

interface Links {
  first: string;
  last: string;
  prev: null;
  next: null;
}

interface Datum {
  id: number;
  fa_name: string;
  en_name: string;
  current_price: number;
  min_price: number;
  max_price: number;
  ratio_price: number;
  diff_price: number;
  properties: Property[];
  brand: Brand;
}

interface Brand {
  id: number;
  name: string;
  icon: string;
}

interface Property {
  id: number;
  name: string;
  value: string;
}
export interface TAddToWishlistSuccessResponse {
  message: string;
}
export interface TAddToWishlistErrorResponse {
  message: string;
}
export interface TDeleteFromWishlistSuccessResponse {
  message: string;
}
export interface TDeleteFromWishlistErrorResponse {
  message: string;
}
