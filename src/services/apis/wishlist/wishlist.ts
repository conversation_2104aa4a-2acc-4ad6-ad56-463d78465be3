import { useInfiniteQuery, useMutation, useQuery } from "@tanstack/react-query";
import queryString from "query-string";
import { CHECK_WISHLIST, DELETE_WISHLIST, POST_WISHLIST, WISHLIST } from "@/constants/apiRoutes";
import { apiService } from "@/services/apiService";
import {
  TAddToWishlistErrorResponse,
  TAddToWishlistSuccessResponse,
  TDeleteFromWishlistErrorResponse,
  TDeleteFromWishlistSuccessResponse,
  TWishlistResponse
} from "./wishlistType";

type TGetWishlistParams = {
  per_page?: number;
  page?: number;
};

export const useWishlistInfiniteQuery = (params?: TGetWishlistParams) =>
  useInfiniteQuery<TWishlistResponse>({
    queryKey: [queryString.stringifyUrl({ url: WISHLIST })],
    queryFn: ({ pageParam }) =>
      apiService
        .get(
          queryString.stringifyUrl({ url: WISHLIST, query: { ...params, page: (pageParam as number) || undefined } })
        )
        .then(({ data }) => data),
    initialPageParam: 1,
    getNextPageParam: (lastPage, page) => {
      if (lastPage.meta?.last_page <= page?.length) return null;

      return lastPage.meta.current_page + 1;
    }
  });

export const useCheckWishlistQuery = (productId?: number) =>
  useQuery<{
    in_wishlist: boolean;
  }>({
    queryKey: [queryString.stringifyUrl({ url: `${CHECK_WISHLIST}/${productId}` })]
  });

export const useAddToWishlistMutation = () =>
  useMutation<TAddToWishlistSuccessResponse, TAddToWishlistErrorResponse, { productId: number }>({
    mutationFn: ({ productId }) => apiService.post(`${POST_WISHLIST}/${productId}`)
  });

export const useDeleteProductFromWishlistMutation = () =>
  useMutation<TDeleteFromWishlistSuccessResponse, TDeleteFromWishlistErrorResponse, { productId: number }>({
    mutationFn: ({ productId }) => apiService.delete(`${DELETE_WISHLIST}/${productId}`)
  });


  