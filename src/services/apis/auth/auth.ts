import queryString from "query-string";
import { useMutation, useQuery } from "@tanstack/react-query";

import { AUTH, AUTH_ME, LOGIN_BY_OTP, REQUEST_OTP } from "@/constants/apiRoutes";
import { AxiosError, AxiosResponse } from "axios";
import { apiService } from "@/services/apiService";
import {
  IAuthMeResponse,
  IRequestOtpResponse,
  IRequestOtpErrorResponse,
  ILoginByOtpErrorResponse,
  ILoginByOtpSuccessResponse,
  IAuthSuccessResponse,
  IAuthErrorResponse,
  IAuthSuccessBody,
  ILoginByOtpBody,
  IRequestOtpBody
} from "./types";

export const useAuthMeQuery = () =>
  useQuery<IAuthMeResponse>({
    queryKey: [queryString.stringifyUrl({ url: AUTH_ME })]
  });

export const useAuthMutation = () =>
  useMutation<AxiosResponse<IAuthSuccessResponse>, AxiosError<IAuthErrorResponse>, IAuthSuccessBody>({
    mutationFn: Body => apiService.post(AUTH, Body)
  });

export const useLoginByOtpMutation = () =>
  useMutation<AxiosResponse<ILoginByOtpSuccessResponse>, AxiosError<ILoginByOtpErrorResponse>, ILoginByOtpBody>({
    mutationFn: Body => apiService.post(LOGIN_BY_OTP, Body)
  });

export const useRequestOtpMutation = () =>
  useMutation<AxiosResponse<IRequestOtpResponse>, AxiosError<IRequestOtpErrorResponse>, IRequestOtpBody>({
    mutationFn: Body => apiService.post(REQUEST_OTP, Body)
  });
