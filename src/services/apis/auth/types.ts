import { IGlobalErrorResponse } from "@/app/types/globalTypes";

export interface IAuthMeData {
  created_at: string;
  email: string;
  id: string;
  is_email_verified: boolean;
  is_phone_number_verified: boolean;
  phone_number: string;
}

export interface IAuthMeResponse {
  data: IAuthMeData;
  status: string;
}

export interface IAuthSuccessResponse {
  data: {
    expires_in: number;
  };
  status: string;
}

export interface IAuthErrorResponse extends IGlobalErrorResponse {}

export interface IAuthSuccessBody {
  username: string;
}

export interface ILoginByOtpBody {
  otp: string;
  username: string;
}

export interface ILoginByOtpSuccessResponse {
  data: {
    expires_in: number;
    token: string;
    user_id: string;
  };
  status: string;
}

export interface ILoginByOtpErrorResponse extends IGlobalErrorResponse {}

export interface IRequestOtpBody {
  username: string;
}

export interface IRequestOtpResponse {
  data: {
    expires_in: number;
  };
}

export interface IRequestOtpErrorResponse extends IGlobalErrorResponse {}
