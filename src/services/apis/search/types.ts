export interface ISearchResponse {
  data: ISearch[];
}

type ISearch = {
  category: {
    icon: string;
    id: string;
    image: string;
    is_active: boolean;
    name: string;
    position: number;
    sub_categories: string[];
  };
  category_id: string;
  cheapest_price: number;
  cheapest_variant: {
    compare_at_price: number;
    created_at: string;
    ean: string;
    id: string;
    is_active: boolean;
    options: {
      additionalProp1: string;
      additionalProp2: string;
      additionalProp3: string;
    };
    price: number;
    product_id: string;
    stock: number;
    updated_at: string;
  };
  cover: {
    alt: string;
    marked_as_cover: boolean;
    url: string;
  };
  description: string;
  featured: boolean;
  handle: string;
  has_variant: boolean;
  id: string;
  images: Array<{
    alt: string;
    marked_as_cover: boolean;
    url: string;
  }>;
  is_active: boolean;
  name: string;
  properties: {
    additionalProp1: string[];
    additionalProp2: string[];
    additionalProp3: string[];
  };
  return_policy: {
    description: string;
    is_allowed: boolean;
    shipping_payer: string;
    window_time: {
      max: number;
      min: number;
    };
  };
  shipping_policies: Array<{
    carrier: string;
    class: string;
    created_at: string;
    description: string;
    excluded: boolean;
    extra_item_rate: number;
    location_id: string;
    prepaid: boolean;
    rate: number;
    shipping_time: {
      max: number;
      min: number;
    };
    updated_at: string;
  }>;
  store_id: string;
  tags: string[];
  variants: Array<{
    compare_at_price: number;
    created_at: string;
    ean: string;
    id: string;
    is_active: boolean;
    options: {
      additionalProp1: string;
      additionalProp2: string;
      additionalProp3: string;
    };
    price: number;
    product_id: string;
    stock: number;
    updated_at: string;
  }>;
};
