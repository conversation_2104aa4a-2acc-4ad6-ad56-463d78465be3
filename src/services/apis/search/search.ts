import queryString from "query-string";
import { SEARCH } from "@/constants/apiRoutes";
import { useQuery } from "@tanstack/react-query";
import { ISearchResponse } from "./types";

type TGetProductsParams = {
  query?: string;
};

export const useSearchesQuery = (params?: TGetProductsParams) =>
  useQuery<ISearchResponse>({
    queryKey: [queryString.stringifyUrl({ url: SEARCH, query: params })]
  });
