import queryString from "query-string";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ADD_PRODUCT, CART, CHECKOUT, REMOVE_PRODUCT } from "@/constants/apiRoutes";
import { apiService } from "@/services/apiService";
import { AxiosError, AxiosResponse } from "axios";
import useCartStore from "@/store/sessionStore";
import {
  IAddToCartBody,
  IAddToCartErrorResponse,
  IAddToCartSuccessResponse,
  ICartResponse,
  ICheckoutResponse,
  IRemoveAllCartBody
} from "./types";

export const useCartQuery = () =>
  useQuery<ICartResponse>({
    queryKey: [queryString.stringifyUrl({ url: CART })],
    staleTime: 0
  });

export const useCheckoutQuery = () =>
  useQuery<ICheckoutResponse>({
    queryKey: [queryString.stringifyUrl({ url: CHECKOUT })]
  });

export const useAddProductMutation = () => {
  const { setCart } = useCartStore();
  const queryClient = useQueryClient();

  return useMutation<AxiosResponse<IAddToCartSuccessResponse>, AxiosError<IAddToCartErrorResponse>, IAddToCartBody>({
    mutationFn: Body => apiService.post(ADD_PRODUCT, Body),
    onSuccess: data => {
      setCart({ cartId: data?.data?.data?.cart_id });
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: [queryString.stringifyUrl({ url: CART })] });
      }, 0);
    }
  });
};

export const useRemoveProductMutation = () => {
  const queryClient = useQueryClient();

  return useMutation<AxiosResponse<IAddToCartSuccessResponse>, AxiosError<IAddToCartErrorResponse>, IAddToCartBody>({
    mutationFn: Body => apiService.post(REMOVE_PRODUCT, Body),
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: [queryString.stringifyUrl({ url: CART })] });
      }, 0);
    }
  });
};

export const useRemoveAllProductMutation = () => {
  const queryClient = useQueryClient();

  return useMutation<AxiosResponse<IAddToCartSuccessResponse>, AxiosError<IAddToCartErrorResponse>, IRemoveAllCartBody>(
    {
      mutationFn: Body => apiService.post(REMOVE_PRODUCT, Body),
      onSuccess: () => {
        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: [queryString.stringifyUrl({ url: CART })] });
        }, 0);
      }
    }
  );
};
