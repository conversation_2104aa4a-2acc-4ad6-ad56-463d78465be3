import { IGlobalErrorResponse } from "@/app/types/globalTypes";

export interface ICartData {
  customer: {
    addresses: {
      address1: string;
      address2: string;
      city: string;
      company: string;
      first_name: string;
      id: string;
      last_name: string;
      location_id: string;
      phone_number: string;
      state: string;
      zip: string;
    }[];
    default_address: string;
    id: string;
    name: string;
    user_id: string;
    wishlist: string[];
  };
  line_items: {
    compare_at_price: number;
    name: string;
    options: Record<string, string>;
    price: number;
    cover: { alt: string; marked_as_cover: boolean; url: string };
    // product: {
    //   cover: {
    //     alt: string;
    //     marked_as_cover: boolean;
    //     url: string;
    //   };
    //   category: {
    //     icon: string;
    //     id: string;
    //     image: string;
    //     is_active: boolean;
    //     name: string;
    //     position: number;
    //     sub_categories: string[];
    //   };
    //   category_id: string;
    //   description: string;
    //   featured: boolean;
    //   handle: string;
    //   id: string;
    //   images: {
    //     alt: string;
    //     marked_as_cover: boolean;
    //     url: string;
    //   }[];
    //   is_active: boolean;
    //   name: string;
    //   return_policy: {
    //     description: string;
    //     is_allowed: boolean;
    //     shipping_payer: string;
    //     window_time: {
    //       max: number;
    //       min: number;
    //     };
    //   };
    //   shipping_policies: {
    //     carrier: string;
    //     class: string;
    //     created_at: string;
    //     description: string;
    //     excluded: boolean;
    //     extra_item_rate: number;
    //     location_id: string;
    //     prepaid: boolean;
    //     rate: number;
    //     shipping_time: {
    //       max: number;
    //       min: number;
    //     };
    //     updated_at: string;
    //   }[];
    //   store_id: string;
    //   tags: string[];
    //   variants: {
    //     compare_at_price: number;
    //     created_at: string;
    //     ean: string;
    //     id: string;
    //     is_active: boolean;
    //     options: Record<string, string>;
    //     price: number;
    //     product_id: string;
    //     stock: number;
    //     updated_at: string;
    //   }[];
    // };
    product_id: string;
    quantity: number;
    variant: {
      compare_at_price: number;
      created_at: string;
      ean: string;
      id: string;
      is_active: boolean;
      options: Record<string, string>;
      price: number;
      product_id: string;
      stock: number;
      updated_at: string;
    };
    variant_id: string;
  }[];
  sub_total_price: number;
  total_price: number;
  total_shipping: number;
  total_tax: number;
}

export interface ICartResponse {
  data: ICartData;
  status: string;
}

export interface IAddToCartBody {
  product_id: string;
  quantity: string;
  variant_id: string;
}

export interface IAddToCartSuccessResponse {
  data: { cart_id: string };
  status: string;
}

export interface IAddToCartErrorResponse extends IGlobalErrorResponse {}

export interface IRemoveAllCartBody {
  product_id: string;
  quantity: string;
  variant_id: string;
}

export interface ICheckoutData {
  payment_methods: [{ id: string; is_active: boolean; key: string; logo: string; name: string; priority: number }];
  vpn: true;
}

export interface ICheckoutResponse {
  status: string;
  data: ICheckoutData;
}
