import queryString from "query-string";
import { SLIDERS } from "@/constants/apiRoutes";
import { useQuery } from "@tanstack/react-query";
import { TSlidersResponse } from "./sliders";

type TGetProductsParams = {
  "Accept-Language"?: "fa" | "en";
};

export const useSlidersQuery = (params?: TGetProductsParams) =>
  useQuery<TSlidersResponse>({
    queryKey: [queryString.stringifyUrl({ url: SLIDERS, query: params })]
  });
