import { PRODUCT_SUGGESTIONS } from "@/constants/apiRoutes";
import { apiService } from "@/services/apiService";
import { useMutation } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";
import { TProductSuggestionSuccessResponse, TProductSuggestionErrorResponse } from "./product-suggestion";

type TProductSuggestionsBody = {
  title: string;
};

export const useProductSuggestionMutation = () =>
  useMutation<
    AxiosResponse<TProductSuggestionSuccessResponse>,
    AxiosError<TProductSuggestionErrorResponse>,
    TProductSuggestionsBody
  >({
    mutationFn: Body => apiService.post(PRODUCT_SUGGESTIONS, Body)
  });
