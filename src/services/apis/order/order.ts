import { ORDER, ORDERS, ORDER_PLACE } from "@/constants/apiRoutes";
import { apiService } from "@/services/apiService";
import { useInfiniteQuery, useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";
import queryString from "query-string";
import {
  IGetOrdersParams,
  IOrderPlaceBody,
  IOrderPlaceErrorResponse,
  IOrderPlaceSuccessResponse,
  IOrderResponse,
  IOrdersResponse
} from "./types";

export const useOrdersInfiniteQuery = (params: IGetOrdersParams) =>
  useInfiniteQuery<IOrdersResponse>({
    queryKey: [queryString.stringifyUrl({ url: ORDERS })],
    queryFn: ({ pageParam }) =>
      apiService
        .get(
          queryString.stringifyUrl({
            url: ORDERS,
            query: { ...params, page: (pageParam as number) || undefined }
          })
        )
        .then(({ data }) => data),
    initialPageParam: 1,
    getNextPageParam: lastPage => {
      const currentPage = lastPage.pagination.page;
      const totalItems = lastPage.pagination.total;
      const pageSize = lastPage.pagination.page_size;
      const totalPages = Math.ceil(totalItems / pageSize);

      if (currentPage >= totalPages) return null;

      return currentPage + 1;
    }
  });

export const useOrderQuery = ({ id }: { id: string }) =>
  useQuery<IOrderResponse>({
    queryKey: [queryString.stringifyUrl({ url: ORDER(id) })],
    enabled:!!id
  });

export const useOrderPlaceMutation = () =>
  useMutation<AxiosResponse<IOrderPlaceSuccessResponse>, AxiosError<IOrderPlaceErrorResponse>, IOrderPlaceBody>({
    mutationFn: Body => apiService.post(ORDER_PLACE, Body)
  });
