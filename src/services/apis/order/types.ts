import { IGlobalErrorResponse } from "@/app/types/globalTypes";

export interface IOrderPlaceBody {
  callback_url: string;
  note?: string;
  payment_method: string;
}

export interface IOrderPlaceErrorResponse extends IGlobalErrorResponse {}

export interface IOrderResponse {
  status: string;
  data: {
    billing_address: Address;
    created_at: string;
    currency: string;
    customer: Customer;
    customer_id: string;
    exchange_rate: number;
    id: string;
    line_items: LineItem[];
    note: string;
    number: string;
    payment_state: PAYMENT_STATE;
    shipping_address: Address;
    shipping_price: number;
    state: ORDER_STATE;
    store: Store;
    store_id: string;
    subtotal_price: number;
    total_price: number;
    transaction: Transaction;
    transaction_id: string;
    updated_at: string;
  };
}

export type Address = {
  address1: string;
  address2: string;
  city: string;
  company: string;
  first_name: string;
  last_name: string;
  location_id: string;
  phone_number: string;
  state: string;
  zip: string;
};

export type Customer = {
  company: string;
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  title: string;
};

export type LineItem = {
  compare_at_price: number;
  name: string;
  options: Record<string, string>;
  price: number;
  product_id: string;
  quantity: number;
  variant: Variant;
  variant_id: string;
  cover: ProductImage;
  images: ProductImage[];
};

export type Product = {
  category: Category;
  category_id: string;
  description: string;
  featured: boolean;
  handle: string;
  id: string;
  images: ProductImage[];
  cover: ProductImage;
  is_active: boolean;
  name: string;
  return_policy: ReturnPolicy;
  shipping_policies: ShippingPolicy[];
  store_id: string;
  tags: string[];
  variants: Variant[];
};

export type Category = {
  icon: string;
  id: string;
  image: string;
  is_active: boolean;
  name: string;
  parent_id: string;
  position: number;
  sub_categories: string[];
};

export type ProductImage = { alt: string; marked_as_cover: boolean; url: string };

export type ReturnPolicy = {
  description: string;
  is_allowed: boolean;
  shipping_payer: "Customer" | string;
  window_time: { max: number; min: number };
};

type ShippingPolicy = {
  carrier: string;
  class: string;
  created_at: string;
  description: string;
  excluded: boolean;
  extra_item_rate: number;
  location_id: string;
  prepaid: boolean;
  rate: number;
  shipping_time: { max: number; min: number };
  updated_at: string;
};

export type Variant = {
  compare_at_price: number;
  created_at: string;
  ean: string;
  id: string;
  is_active: boolean;
  options: Record<string, string>;
  price: number;
  product_id: string;
  stock: number;
  updated_at: string;
};

export type Store = {
  contact_email: string;
  contact_number: string;
  currency: Currency;
  description: string;
  domain: string;
  handle: string;
  id: string;
  instagram_id: string;
  is_active: boolean;
  locale: Locale;
  logo: string;
  name: string;
  slogan: string;
  telegram_id: string;
  whatsapp_number: string;
};

export type Currency = {
  iso: string;
  name: string;
  precision: number;
  symbol: string;
  symbol_position: "after" | "before" | string;
};

export type Locale = {
  code: string;
  country_code: string;
  direction: "ltr" | "rtl" | string;
  language_iso: string;
  money_separator: string;
  name: string;
  native_name: string;
};

export type Transaction = {
  amount: number;
  authorize_only: boolean;
  currency: string;
  data: Record<string, string>;
  detail: string;
  id: string;
  kind: "Payment" | string;
  paymentMethod: PaymentMethod;
  payment_method_id: string;
  reference: string;
  status: "Pending" | string;
  store_id: string;
  tracking_code: string;
};

export type PaymentMethod = {
  config: number[];
  id: string;
  is_active: boolean;
  key: string;
  logo: string;
  name: string;
  priority: number;
};

export interface IGetOrdersParams {
  page_size?: number;
  page?: number;
}

export interface IOrdersResponse {
  status: string;
  data: Order[];
  pagination: { page: number; page_size: number; total: number };
}

export type ORDER_STATE =
  | "PaymentPending"
  | "PaymentFailed"
  | "Pending"
  | "InProgress"
  | "Done"
  | "Cancelled"
  | "Refunded";
export type PAYMENT_STATE = "PaymentPending" | "PaymentFailed" | "Paid";

export type Order = {
  billing_address: Address;
  created_at: string;
  currency: string;
  customer: Customer;
  customer_id: string;
  exchange_rate: number;
  id: string;
  line_items: LineItem[];
  note: string;
  number: string;
  payment_state: PAYMENT_STATE;
  shipping_address: Address;
  shipping_price: number;
  state: ORDER_STATE;
  store: Store;
  store_id: string;
  subtotal_price: number;
  total_price: number;
  transaction: Transaction;
  transaction_id: string;
  updated_at: string;
};

export interface IOrderPlaceSuccessResponse {
  data: { id: string; url: string };
  status: string;
}
