import { IGlobalErrorResponse } from "@/app/types/globalTypes";

export interface Address {
  address1: string;
  address2: string;
  city: string;
  company: string;
  first_name: string;
  id: string;
  last_name: string;
  location_id: string;
  phone_number: string;
  state: string;
  zip: string;
}

interface User {
  created_at: string;
  email: string;
  id: string;
  is_email_verified: boolean;
  is_phone_number_verified: boolean;
  phone_number: string;
}

interface IProfileData {
  addresses: Address[];
  default_address: string;
  id: string;
  name: string;
  user: User;
  user_id: string;
  wishlist: string[];
}

export interface IProfileResponse {
  data: IProfileData;
  status: string;
}

export interface IAddressBody {
  address?: string;
  contact_number?: string;
  first_name?: string;
  last_name?: string;
  location_id?: string;
  zip?: string;
  id?: string;
}

export interface IAddressErrorResponse extends IGlobalErrorResponse {}

export interface IAddressData {
  address1: string;
  address2: string;
  city: string;
  company: string;
  first_name: string;
  id: string;
  last_name: string;
  location_id: string;
  phone_number: string;
  state: string;
  zip: string;
}

export interface IAddressResponse {
  data: IAddressData[];
  status: string;
}

export interface INameErrorResponse extends IGlobalErrorResponse {}

export interface INameBody {
  name: string;
}
// 1. Separate interfaces for better organization
interface ProductCategory {
  icon: string;
  id: string;
  image: string;
  is_active: boolean;
  name: string;
  position: number;
  sub_categories: string[];
}

interface ProductVariant {
  compare_at_price: number;
  created_at: string;
  ean: string;
  id: string;
  is_active: boolean;
  options: Record<string, string>;
  price: number;
  product_id: string;
  stock: number;
  updated_at: string;
}

interface ProductImage {
  alt: string;
  marked_as_cover: boolean;
  url: string;
}

interface ShippingPolicy {
  carrier: string;
  class: string;
  created_at: string;
  description: string;
  excluded: boolean;
  extra_item_rate: number;
  location_id: string;
  prepaid: boolean;
  rate: number;
  shipping_time: {
    max: number;
    min: number;
  };
  updated_at: string;
}

// 2. Union type for shipping payer
type ShippingPayer = "Customer" | "Merchant" | "ThirdParty";

export interface IWishListData {
  category: ProductCategory;
  category_id: string;
  cheapest_price: number;
  cheapest_variant: ProductVariant;
  cover: ProductImage;
  description: string;
  featured: boolean;
  handle: string;
  has_variant: boolean;
  id: string;
  images: ProductImage[];
  is_active: boolean;
  name: string;
  properties: Record<string, string[]>;
  return_policy: {
    description: string;
    is_allowed: boolean;
    shipping_payer: ShippingPayer;
    window_time: {
      max: number;
      min: number;
    };
  };
  shipping_policies: ShippingPolicy[];
  store_id: string;
  tags: string[];
  variants: ProductVariant[];
}

export interface IIWishListResponse {
  data: IWishListData[];
  status: string;
}

export interface IWishListErrorResponse extends IGlobalErrorResponse {}

export interface IIWishListBody {
  product_id: string;
}

export interface ISelectAddressBody {
  id: string;
}
