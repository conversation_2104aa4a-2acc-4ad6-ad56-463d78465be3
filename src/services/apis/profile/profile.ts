import { ADDRESS, NAME, PRO<PERSON>LE, SELECT_ADDRESS, WISHLIST } from "@/constants/apiRoutes";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import queryString from "query-string";
import { apiService } from "@/services/apiService";
import { AxiosError, AxiosResponse } from "axios";
import { TDeleteFromWishlistErrorResponse } from "../wishlist/wishlistType";
import {
  IAddressBody,
  IAddressErrorResponse,
  IAddressResponse,
  IIWishListBody,
  IIWishListResponse,
  INameBody,
  INameErrorResponse,
  IProfileResponse,
  ISelectAddressBody,
  IWishListErrorResponse
} from "./types";

export const useProfileQuery = () =>
  useQuery<IProfileResponse>({
    queryKey: [queryString.stringifyUrl({ url: PROFILE })],
    staleTime: 0
  });

export const useAddressQuery = () =>
  useQuery<IAddressResponse>({
    queryKey: [queryString.stringifyUrl({ url: ADDRESS })]
  });

export const useAddressMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<AxiosResponse<any>, AxiosError<IAddressErrorResponse>, IAddressBody>({
    mutationFn: Body => apiService.put(ADDRESS, Body),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryString.stringifyUrl({ url: PROFILE })] });
    }
  });
};

export const useNameMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<AxiosResponse<any>, AxiosError<INameErrorResponse>, INameBody>({
    mutationFn: Body => apiService.put(NAME, Body),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryString.stringifyUrl({ url: PROFILE })] });
    }
  });
};

// needs to update
export const useSelectAddressMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<AxiosResponse<any>, AxiosError<INameErrorResponse>, ISelectAddressBody>({
    mutationFn: Body => apiService.post(SELECT_ADDRESS, Body),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryString.stringifyUrl({ url: PROFILE })] });
    }
  });
};

export const useWishListQuery = () =>
  useQuery<IIWishListResponse>({
    queryKey: [queryString.stringifyUrl({ url: WISHLIST })]
  });

// needs to update
export const useWishListMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<AxiosResponse<any>, AxiosError<IWishListErrorResponse>, IIWishListBody>({
    mutationFn: Body => apiService.post(WISHLIST, Body),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryString.stringifyUrl({ url: WISHLIST })] });
    }
  });
};

export const useDeleteWishlistMutation = () => {
  const queryClient = useQueryClient();

  return useMutation<any, TDeleteFromWishlistErrorResponse, { product_id: string }>({
    mutationFn: ({ product_id }) => apiService.delete(WISHLIST, { data: { product_id } }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryString.stringifyUrl({ url: WISHLIST })] });
    }
  });
};
