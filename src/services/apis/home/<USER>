import { CATEGORIES, STORE } from "@/constants/apiRoutes";
import { useQuery } from "@tanstack/react-query";
import queryString from "query-string";
import { ICategoriesResponse, IStoreResponse } from "./types";

export const useCategoriesQuery = () =>
  useQuery<ICategoriesResponse>({
    queryKey: [queryString.stringifyUrl({ url: CATEGORIES })]
  });

export const useStoreQuery = () =>
  useQuery<IStoreResponse>({
    queryKey: [queryString.stringifyUrl({ url: STORE })]
  });
