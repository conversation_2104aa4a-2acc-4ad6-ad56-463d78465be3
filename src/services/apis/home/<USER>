export interface ICategoriesData {
  icon: string;
  id: string;
  image: string;
  is_active: boolean;
  name: string;
  position: number;
  sub_categories: string[];
}

export interface ICategoriesResponse {
  data: ICategoriesData[];
  status: string;
}

export interface IStoreData {
  contact_email: string;
  contact_number: string;
  currency: {
    iso: string;
    name: string;
    precision: number;
    symbol: string;
    symbol_position: "after" | "before";
  };
  description: string;
  domain: string;
  handle: string;
  id: string;
  instagram_id: string;
  is_active: boolean;
  locale: {
    code: string;
    country_code: string;
    direction: "ltr" | "rtl";
    language_iso: string;
    money_separator: string;
    name: string;
    native_name: string;
  };
  logo: string;
  name: string;
  slogan: string;
  telegram_id: string;
  whatsapp_number: string;
}

export interface IStoreResponse {
  data: IStoreData;
  status: string;
}
