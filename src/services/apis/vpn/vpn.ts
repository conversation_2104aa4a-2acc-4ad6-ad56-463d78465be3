import axios from "axios";
import { useQuery } from "@tanstack/react-query";

const checkVpn = async () => {
  try {
    const { data } = await axios.get("https://ipapi.co/json/");

    const isVpnLikely =
      data.org?.toLowerCase().includes("vpn") ||
      data.org?.toLowerCase().includes("proxy") ||
      data.country_code !== "IR";
    return isVpnLikely;
  } catch (error) {
    console.error("Error checking VPN:", error);
    return false;
  }
};

const useVpnCheck = () => {
  const {
    data: isVpn,
    isLoading,
    error
  } = useQuery({
    queryKey: ["vpn-check"],
    queryFn: checkVpn
  });

  return { isVpn, isLoading, error };
};

export default useVpnCheck;
