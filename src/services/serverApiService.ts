// serverApiService.ts
import { ACCESS_TOKEN, STORE_HANDLE } from "@/constants/cookies";
// import useCartStore from "@/store/sessionStore";
import { logout } from "@/utils/logout";
import axios, { AxiosError, InternalAxiosRequestConfig } from "axios";

export const baseURL = process.env.NEXT_PUBLIC_BASE_URL;

// Create a factory function that accepts cookies as parameters
export const createServerApiService = (storeHandle?: string) => {
  const serverApiService = axios.create({
    baseURL,
    timeout: 10000,
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      "accept-language": "fa",
      ...(process.env.NODE_ENV === "development" ? { "X-Requested-With": "localhost" } : {})
    },
    withCredentials: true
  });

  serverApiService.interceptors.request.use((request: InternalAxiosRequestConfig) => {
    // const cartId = useCartStore.getState()?.cartId;

    // if (cartId) {
    //   request.headers.set("x-cart-id", cartId);
    // }

    if (storeHandle) {
      request.headers.set("X-Store-Handle", `${storeHandle}`);
    }

    return request;
  });

  serverApiService.interceptors.response.use(
    response => response,
    async (error: AxiosError) => {
      try {
        const { config, response } = error;

        return await Promise.reject(error);
      } catch (err) {
        return Promise.reject(err);
      }
    }
  );

  return serverApiService;
};
