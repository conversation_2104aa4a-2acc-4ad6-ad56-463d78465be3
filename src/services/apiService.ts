import { ACCESS_TOKEN } from "@/constants/cookies";
import { STORE_HANDLE_REGEX } from "@/constants/regexs";
import { getCookie } from "@/hooks/useCookie";
import useCartStore from "@/store/sessionStore";
import { logout } from "@/utils/logout";
import axios, { AxiosError } from "axios";

export const baseURL = process.env.NEXT_PUBLIC_BASE_URL;

export const apiService = axios.create({
  baseURL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
    "accept-language": "fa",
    ...(process.env.NODE_ENV === "development" ? { "X-Requested-With": "localhost" } : {})
  },
  withCredentials: true
});

apiService.interceptors.request.use(request => {
  const accessToken = getCookie(ACCESS_TOKEN);
  const cartId = useCartStore.getState()?.getCart()?.cartId;

  if (cartId) {
    request.headers.set("x-cart-id", cartId);
  }

  if (accessToken) {
    request.headers.setAuthorization(`Bearer ${accessToken}`);
  }

  const storeRegex = STORE_HANDLE_REGEX;
  const { pathname, host } = window.location || {};
  const storeHandleMath = pathname.match(storeRegex);
  const storeHandle = storeHandleMath || host;

  if (process.env.NODE_ENV === "development") {
    request.headers.set("X-Store-Handle", process.env.NEXT_PUBLIC_TEST_STORE);
    // request.headers.set("X-Store-Handle", storeHandle);
  } else {
    request.headers.set("X-Store-Handle", storeHandle);
  }

  if (request.headers && accessToken) {
    request.headers.Authorization = `Bearer ${accessToken}`;
  }

  return request;
});

apiService.interceptors.response.use(
  response => response,
  async (error: AxiosError) => {
    try {
      const { config, response } = error;

      const token = getCookie(ACCESS_TOKEN);
      const clearCart = useCartStore.getState()?.clearCart;

      // // i.e. if the error is a network error, then we will show a notification
      // if (message === "Network Error" && typeof window !== "undefined") {
      //   notification({
      //     key: "error",
      //     message: i18n?.t("common:network_error") as string,
      //     color: "error"
      //   });
      // }

      if (config && token && response && response.status === 417) {
        clearCart();
      }

      if (config && token && response && response.status === 401) {
        logout();
        if (typeof window !== "undefined") {
          window.location.reload();
        }
      }

      return await Promise.reject(error);
    } catch (err) {
      return Promise.reject(err);
    }
  }
);
