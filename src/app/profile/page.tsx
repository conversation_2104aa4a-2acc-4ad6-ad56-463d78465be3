import Profile from "@/components/containers/profile/Profile";
import { STORE_HANDLE } from "@/constants/cookies";
import { generalMetaTags } from "@/utils/generateGeneralMetaTags";
import { getData } from "@/utils/getServerCookie";

export async function generateMetadata() {
  const storeHandle = await getData(STORE_HANDLE);
  return generalMetaTags({ pageName: "پروفایل", storeHandle });
}

function ProfilePage() {
  return <Profile />;
}

export default ProfilePage;
