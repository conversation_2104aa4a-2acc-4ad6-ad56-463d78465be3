import useLocations from "@/components/ui/locationSelect/useLocations";
import { Product } from "@/services/apis/product/types";
import LocationIcon from "@/assets/icons/location-mini.svg";
import { useTranslations } from "next-intl";
import { thousandSeparator } from "@/utils/number";
import { TOMAN_CURRENCY } from "@/constants/constacts";

interface IProductShippingProps {
  shipping?: Product["shipping_policies"];
}

function ProductShipping({ shipping }: IProductShippingProps) {
  const { getLocation } = useLocations();
  const t = useTranslations();

  const notExcludedShippings = shipping?.filter(item => !item?.excluded);
  const excludedShippings = shipping?.filter(item => item?.excluded);

  return (
    <div className="mt-3 flex flex-col gap-3">
      {notExcludedShippings?.map(item => (
        <div className="flex flex-col" key={item?.class}>
          <div className="flex items-center gap-1 rounded-lg bg-surface-thertiary p-2">
            <LocationIcon />
            <span className="text-xs font-medium">
              {item?.location_id === "00000000-0000-0000-0000-000000000000"
                ? t("product.allCities")
                : getLocation(item?.location_id || "")?.name}
            </span>
          </div>
          <div className="mt-1 grid grid-cols-3 gap-2 py-1">
            <div className="flex flex-col gap-1">
              <span className="text-10 text-content-tertiary">{t("product.shippingTime")}</span>
              {item?.shipping_time?.min === 0 ? (
                <span className="text-xs">
                  {item?.shipping_time?.max} {t("general.dayWorking")}
                </span>
              ) : (
                <span className="text-xs">
                  {t("general.from")} {item?.shipping_time?.min} {t("general.to")} {item?.shipping_time?.max}{" "}
                  {t("general.dayWorking")}
                </span>
              )}
            </div>
            <div className="flex flex-col gap-1">
              <span className="text-10 text-content-tertiary">{t("product.shippingType")}</span>
              <div className="flex items-center gap-0.5">
                <span className="text-xs font-medium">{item?.carrier}</span>
                {item?.prepaid ? (
                  <div className="whitespace-nowrap rounded-full bg-surface-warining-1 px-2 py-0.5 text-10 text-content-on-warning-2">
                    {t("product.prepaid")}
                  </div>
                ) : (
                  <div className="whitespace-nowrap rounded-full bg-surface-action-light px-2 py-0.5 text-10 text-content-on-info">
                    {t("product.nextPaid")}
                  </div>
                )}
              </div>
            </div>

            <div className="flex flex-col gap-1">
              <span className="text-10 text-content-tertiary">{t("product.shippingCost")}</span>
              <div className="flex items-center gap-0.5">
                {item?.rate <= 0 ? (
                  <span className="text-xs font-medium">{t("general.free")}</span>
                ) : (
                  <span className="text-xs font-medium">
                    {thousandSeparator(item?.rate)}{" "}
                    <span className="text-10 text-content-tertiary">{TOMAN_CURRENCY}</span>
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      ))}

      {!!excludedShippings?.length && (
        <div className="flex flex-wrap items-center gap-2.5 rounded-lg bg-surface-info px-2 py-1.5 text-content-on-info">
          <span className="text-xs font-medium">{t("product.noShipping")}</span>
          {excludedShippings?.map(item => (
            <div className="flex items-center">
              <span className="text-xs font-medium">{getLocation(item?.location_id)?.name}</span>
              <span className="last:hidden">،</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default ProductShipping;
