// import { PRODUCT } from "@/constants/apiRoutes";
// import { serverApiService } from "@/services/serverApiService";
// import { stripHtmlTags } from "@/utils/helpers";

// const ProductHead = async ({ params }: { params: { id: string } }) => {
//   const product = await serverApiService
//     .get(PRODUCT(params.id))
//     .then(res => res.data?.data)
//     .catch(() => null);
//   const description = stripHtmlTags(product?.description || "").slice(0, 160);

//   if (!product?.id) return null;

//   const jsonLd = {
//     "@context": "https://schema.org/",
//     "@type": "Product",
//     name: product.name,
//     image: [product.cover?.url],
//     description,
//     sku: product.cheapestVariant?.sku,
//     offers: {
//       "@type": "Offer",
//       url: product.url,
//       priceCurrency: "IRT",
//       price: product.cheapestVariant?.price,
//       itemCondition: "https://schema.org/NewCondition",
//       availability: "https://schema.org/InStock"
//     }
//   };

//   return <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />;
// };

// export default ProductHead;
