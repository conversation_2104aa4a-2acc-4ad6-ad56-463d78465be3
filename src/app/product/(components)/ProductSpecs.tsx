import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface IProductSpecsProps {
  description?: string;
}

function ProductSpecs({ description }: IProductSpecsProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getPreviewText = () => {
    if (!description) return "";

    // Create a temporary element to strip HTML tags
    const tempElement = document.createElement("div");
    tempElement.innerHTML = description;
    const plainText = tempElement.textContent || tempElement.innerText || "";

    // Now safely split into sentences
    const sentences = plainText.split(".");
    const preview = `${sentences.slice(0, 2).join(".")}.`;

    return preview;
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="">
      <div className="mb-3 flex border-b border-b-border-secondary">
        <div className="relative px-4 py-2 text-13 font-medium text-content-on-info-2">
          مشخصات محصول
          <div className="absolute bottom-0 left-0 right-0 h-0.5 w-full rounded-tl-[4px] rounded-tr-[4px] bg-content-on-info-2" />
        </div>
      </div>

      <div className="mb-4 leading-relaxed text-gray-700">
        {getPreviewText()?.length >= 250 ? (
          <>
            {!isExpanded ? (
              <p className="text-xs leading-[18px]">{getPreviewText()}...</p>
            ) : (
              <AnimatePresence>
                <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3 }}>
                  <p className="text-xs leading-[18px]">
                    <div dangerouslySetInnerHTML={{ __html: description || "" }} />
                  </p>
                </motion.div>
              </AnimatePresence>
            )}
          </>
        ) : (
          <p className="text-xs leading-[18px]">{getPreviewText()}</p>
        )}
      </div>

      {getPreviewText()?.length >= 250 && (
        <div className="flex justify-center">
          <button
            type="button"
            onClick={toggleExpand}
            className="flex items-center justify-center gap-1.5 rounded-md bg-surface-thertiary px-4 py-2 text-10 font-medium text-content-tertiary transition-colors duration-200"
          >
            <span className="text-10 text-content-tertiary">{isExpanded ? "نمایش کمتر" : "نمایش بیشتر"}</span>
            <motion.svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-content-subtle"
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              <polyline points="6 9 12 15 18 9" />
            </motion.svg>
          </button>
        </div>
      )}
    </div>
  );
}

export default ProductSpecs;
