"use client";

import { SHOPPING_CART } from "@/constants/nextjsRoutes";
import Link from "next/link";
import ArrowRight from "@/assets/icons/arrow-right.svg";
import { useParams, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import BottomBar from "@/components/ui/bottomBar";
import PriceDisplay from "@/components/ui/priceDisplay/PriceDisplay";
import Button from "@/components/ui/button";
import Plus from "@/assets/icons/plus.svg";
import Verified from "@/assets/icons/verified-check.svg";
import Share from "@/assets/icons/Share.svg";
import Heart from "@/assets/icons/heart.svg";
import FilledHeart from "@/assets/icons/filled-heart.svg";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import Image from "next/image";
import CartBadge from "@/components/ui/cartBadge/cartBadge";
import { useProductQuery } from "@/services/apis/product/product";
import { twMerge } from "tailwind-merge";
import { useState, useEffect } from "react";
import { calculateDiscount } from "@/app/(home)/(components)/utils";
import Loading from "@/components/ui/loading";
import { useCartQuery } from "@/services/apis/cart/cart";
import { useCartModification } from "@/hooks/useCartModification";
import CartModification from "@/components/ui/cartModification/CartModification";
import { useDeleteWishlistMutation, useWishListMutation, useWishListQuery } from "@/services/apis/profile/profile";
import { handleErrorResponse } from "@/utils/handleError";
import { getCookie } from "@/hooks/useCookie";
import { ACCESS_TOKEN } from "@/constants/cookies";
import Auth from "@/components/containers/auth/Auth";
import ProductSpecs from "./ProductSpecs";
import ProductSlider from "./ProductSlider";
import ProductShipping from "./ProductShipping";
import ProductReturn from "./ProductReturn";

function ProductDetail() {
  const t = useTranslations();
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;
  const [policyState, setPolicyState] = useState("shipping");

  const accessToken = getCookie(ACCESS_TOKEN);

  // const id = "e1ed9f0d-1d5a-4cb9-899c-e846ff30f30c";
  const { open, close } = useActionSheet();
  const [selectedVariant, setSelectedVariant] = useState({});
  const [cartCount, setCartCount] = useState(0);

  const { data: product, isLoading: isLoadingProduct } = useProductQuery({ id });
  const { data: wishList } = useWishListQuery();

  const { mutate: addWishListMutate, isPaused: isLoadingAddWishList } = useWishListMutation();
  const { mutate: removeWishListMutate, isPaused: isLoadingAddWishListDelete } = useDeleteWishlistMutation();

  const isLoadingWishListMutate = isLoadingAddWishList || isLoadingAddWishListDelete;

  const { data: carts } = useCartQuery();

  const isInWishList = wishList?.data?.find(item => item?.id === id);

  const { handleAddProduct, handleRemoveProduct } = useCartModification({
    variants: product?.data?.variants || [],
    onChangeCartCount: setCartCount
  });

  const onOpenLogin = () => {
    open(
      <div>
        <Auth />
      </div>,
      {
        closable: false,
        fitHeight: true,
        bottomClose: true,
        backdropBlur: true,
        events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
      }
    );
  };

  const onAddWishList = async () => {
    if (!accessToken) {
      onOpenLogin();
      return;
    }

    try {
      await addWishListMutate(
        { product_id: id },
        {
          onSuccess: () => {},
          onError(error) {
            if (error) {
              handleErrorResponse({ error, t });
            }
          }
        }
      );
    } catch (error: any) {
      handleErrorResponse({ error, t });
    }
  };

  const onRemoveWishList = async () => {
    if (!accessToken) {
      onOpenLogin();
      return;
    }

    try {
      removeWishListMutate(
        { product_id: id },
        {
          onSuccess: () => {},
          onError(error) {
            if (error) {
              handleErrorResponse({ error: error as any, t });
            }
          }
        }
      );
    } catch (error: any) {
      handleErrorResponse({ error, t });
    }
  };

  const variant = product?.data?.variants.find(variant =>
    Object?.keys(selectedVariant).every(key => variant?.options?.[key] === (selectedVariant as any)?.[key])
  );

  const cart = carts?.data?.line_items?.find(item => item?.variant_id === variant?.id);

  const variantKeys = Object?.keys(product?.data?.properties ?? {});
  const selectedVariantValues = Object?.values(selectedVariant ?? {});

  const handleAddToCart = () => {
    open(
      <div className="px-4">
        <div className="mt-4 border-t border-t-border-primary py-6">
          <div>
            <div className="flex gap-4">
              <div className="relative size-16 rounded border border-[#F3F4F6]">
                <Image src={product?.data?.cover?.url ?? ""} alt={id} fill className="object-contain" />
              </div>

              <div className="flex flex-col gap-4">
                <span className="text-xs font-medium leading-[18px] text-content-primary">{product?.data?.name}</span>
                <div className="flex items-center gap-2 text-xs text-content-secondary">
                  {selectedVariantValues?.map((item: any) => (
                    <div key={item} className="border-l border-l-border-primary last:border-l-transparent">
                      <p className="pl-2">{item}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <Link prefetch href={SHOPPING_CART}>
            <Button size="lg" className="mt-6 w-full" onClick={close}>
              {t("product.goToCart")}
            </Button>
          </Link>
        </div>
      </div>,
      {
        closable: false,
        fitHeight: true,
        bottomClose: true,
        backdropBlur: true,
        title: (
          <div className="flex items-center gap-1">
            <Verified />
            <span className="text-sm font-semibold text-content-on-success-2">{t("product.addToCartPopupTitle")}</span>
          </div>
        ),
        events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
      }
    );
  };

  const onShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({ title: product?.data?.name, text: "", url: window.location.href });
      } catch (error) {
        // do something
      }
    }
  };

  useEffect(() => {
    if (cart?.variant_id) setCartCount(cart?.quantity);
  }, [cart]);

  useEffect(() => {
    if (product?.data?.cheapest_variant?.options) {
      setSelectedVariant(product?.data?.cheapest_variant?.options);
    }
  }, [product]);

  return (
    <div className="flex h-full w-full flex-col gap-3 bg-surface-primary">
      <div className="flex h-14 flex-col bg-surface-primary py-3">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-2">
            <ArrowRight className="cursor-pointer" onClick={() => router.back()} />

            <span className="text-sm">{t("product.productDetailTitle")}</span>
          </div>

          <div className="flex items-center gap-3">
            <Link prefetch href={SHOPPING_CART}>
              <CartBadge />
            </Link>

            <Share className="cursor-pointer" onClick={onShare} />
            {isLoadingWishListMutate ? (
              <Loading size="xs" />
            ) : isInWishList ? (
              <FilledHeart className="cursor-pointer" onClick={onRemoveWishList} />
            ) : (
              <Heart className="cursor-pointer" onClick={onAddWishList} />
            )}
          </div>
        </div>
      </div>

      {isLoadingProduct ? (
        <div className="flex h-full w-full items-center justify-center">
          <Loading />
        </div>
      ) : !product?.data?.id ? (
        <div className="flex w-full flex-1 items-center justify-center bg-surface-primary">
          <span className="text-sm text-content-secondary">{t("home.noProducts")}</span>
        </div>
      ) : (
        <div className="w-full overflow-auto">
          <ProductSlider images={product?.data?.images} isLoading={isLoadingProduct} />

          <div className="border-b border-b-border-secondary p-4">
            <p className="text-sm text-content-primary">{product?.data?.name}</p>

            <div className="mt-4 flex items-center gap-0.5">
              <span className="text-xs text-content-tertiary">{t("product.category")}:</span>
              <Link prefetch className="text-xs text-content-primary" href={`/${product?.data?.category_id}`}>
                {product?.data?.category?.name}
              </Link>
            </div>
          </div>

          {!!variantKeys?.length && (
            <div className="border-b border-b-border-secondary p-4">
              <div className="flex flex-col gap-4">
                {variantKeys?.map(vItem => (
                  <div className="flex flex-col">
                    <span className="text-xs font-medium">{vItem}</span>
                    <div className="mt-1.5 flex items-center gap-2">
                      {product?.data?.properties[vItem]?.map(property => (
                        <div
                          className={twMerge(
                            "cursor-pointer rounded-full border border-border-primary px-3 py-2 text-13 font-medium",
                            (selectedVariant as any)?.[vItem] === property
                              ? "border-content-on-info bg-surface-info !text-13 !font-medium text-content-on-info"
                              : ""
                          )}
                          onClick={() => setSelectedVariant(prev => ({ ...prev, [vItem]: property }))}
                        >
                          {property}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="px-4 pt-4">
            <ProductSpecs description={product?.data?.description} />
          </div>

          <div className="mb-6 mt-4 px-4">
            <div className="flex border-b border-b-border-secondary">
              <div
                className={twMerge(
                  "relative cursor-pointer px-4 py-2 !text-13 font-medium",
                  policyState === "shipping" ? "text-content-on-info-2" : "text-content-primary"
                )}
                onClick={() => {
                  setPolicyState("shipping");
                }}
              >
                {t("product.shipping")}
                {policyState === "shipping" && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 w-full rounded-tl-[4px] rounded-tr-[4px] bg-content-on-info-2" />
                )}
              </div>
              <div
                className={twMerge(
                  "relative cursor-pointer px-4 py-2 !text-13 font-medium",
                  policyState === "return" ? "text-content-on-info-2" : "text-content-primary"
                )}
                onClick={() => {
                  setPolicyState("return");
                }}
              >
                {" "}
                {t("product.return")}
                {policyState === "return" && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 w-full rounded-tl-[4px] rounded-tr-[4px] bg-content-on-info-2" />
                )}
              </div>
            </div>

            {policyState === "shipping" ? (
              <ProductShipping shipping={product?.data?.shipping_policies} />
            ) : (
              <ProductReturn data={product?.data?.return_policy} />
            )}
          </div>
        </div>
      )}

      {!!product?.data?.id && (
        <BottomBar className="h-20 shadow-sm">
          <div className="flex w-full items-center justify-between">
            <PriceDisplay
              finalPrice={variant?.price ?? 0}
              discountPercentage={calculateDiscount(variant?.price ?? 0, variant?.compare_at_price ?? 0) ?? 0}
              originalPrice={variant?.compare_at_price}
              percentageClassName="flex-row-reverse"
              finalPriceClassName="text-xl font-bold"
              finalPriceWrapperClassName="text-right justify-start"
            />
            {cartCount > 0 ? (
              <CartModification
                cartCount={cartCount}
                onMinimize={value => {
                  setCartCount(value);
                  handleRemoveProduct(value);
                }}
                onPlus={value => {
                  setCartCount(value);
                  handleAddProduct(value);
                }}
              />
            ) : (
              <Button
                size="md"
                className="!h-12"
                startAdornment={<Plus className="[&>path]:stroke-white" />}
                onClick={() => {
                  setCartCount(cartCount + 1);
                  handleAddProduct(cartCount + 1);

                  setTimeout(() => {
                    handleAddToCart();
                  }, 0);
                }}
              >
                {t("product.addToCart")}
              </Button>
            )}
          </div>
        </BottomBar>
      )}
    </div>
  );
}

export default ProductDetail;
