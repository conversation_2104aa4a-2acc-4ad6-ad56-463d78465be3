import { Product } from "@/services/apis/product/types";
import NoReturn from "@/assets/icons/no-return.svg";
import InfoReturn from "@/assets/icons/info-return.svg";
import InfoReturnBlue from "@/assets/icons/info-return-blue.svg";
import ReturnPolicy from "@/assets/icons/return-policy.svg";
import { useTranslations } from "next-intl";

interface IProductReturnProps {
  data?: Product["return_policy"];
}

function ProductReturn({ data }: IProductReturnProps) {
  const t = useTranslations();

  if (!data?.is_allowed) {
    return (
      <div className="pt-4">
        <div className="flex items-center justify-center">
          <NoReturn />
        </div>

        <div className="mt-2 flex items-center gap-2 rounded-lg bg-surface-warining-1 px-3 py-2">
          <InfoReturn />
          <span className="text-xs font-medium text-content-on-warning-2">{t("product.noReturn")}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-4">
      <div className="mt-2 flex items-center gap-2 rounded-lg bg-surface-info px-3 py-2">
        <InfoReturnBlue />
        <span className="text-xs font-medium text-content-on-info">{t("product.returnAlert")}</span>
      </div>

      <div className="mt-4 flex items-center justify-between">
        <span className="text-sm font-medium text-content-primary">{t("product.returnTime")}</span>

        <div className="rounded-full bg-content-on-success-2 px-2 py-1 text-sm font-medium text-content-on-action-hover-1">
          {t("general.till")} {data?.window_time?.max} {t("general.dayWorking")}
        </div>
      </div>

      <div className="mt-4">
        <div className="flex items-center gap-1">
          <ReturnPolicy />
          <span className="text-sm font-medium">{t("product.returnPolicy")}</span>
        </div>

        <div className="mt-2 text-13 text-content-secondary">{data?.description}</div>
      </div>
    </div>
  );
}

export default ProductReturn;
