"use client";

import Slider from "@/components/ui/slider";
import { Product } from "@/services/apis/product/types";
import Image from "next/image";
import React from "react";

interface IProductSliderProps {
  images?: Product["images"];
  isLoading?: boolean;
}

function ProductSlider({ images, isLoading }: IProductSliderProps) {
  if (isLoading) {
    return <div className="bg-surface-promary h-[270px]" />;
  }

  if (!images?.length) {
    return null;
  }

  return (
    <div className="relative w-full overflow-hidden">
      <Slider
        slides={
          images?.map(item => (
            <div key={item?.url} className="relative h-[270px] w-full overflow-hidden">
              <Image fill alt={item?.alt} src={item.url} className="h-full w-full object-contain" />
            </div>
          )) || []
        }
        swiperProps={{
          slidesPerView: 1,
          spaceBetween: -20,
          centeredSlides: true,
          className: "[&>.swiper-pagination]:!left-unset [&>.swiper-pagination]:!text-center "
          //   centeredSlidesBounds: true,
        }}
      />
    </div>
  );
}

export default ProductSlider;
