import { STORE_HANDLE } from "@/constants/cookies";
import { generateProductMetaTags } from "@/utils/generateProductMetaTags";
import { getData } from "@/utils/getServerCookie";
import ProductDetail from "../(components)/ProductDetail";
// import ProductHead from "../(components)/ProductHead";

export async function generateMetadata({ params }: { params: { id: string } }) {
  const storeHandle = await getData(STORE_HANDLE);

  return generateProductMetaTags({ params, storeHandle });
}

// export async function generateProductHead({ params }: { params: { id: string } }) {
//   return ProductHead({ params });
// }

function ProductPage() {
  return <ProductDetail />;
}

export default ProductPage;
