import Address from "@/components/containers/address/Address";
import { STORE_HANDLE } from "@/constants/cookies";
import { generalMetaTags } from "@/utils/generateGeneralMetaTags";
import { getData } from "@/utils/getServerCookie";

export async function generateMetadata() {
  const storeHandle = await getData(STORE_HANDLE);
  return generalMetaTags({ pageName: "آدرس‌ها", storeHandle });
}

function AddressPage() {
  return <Address />;
}

export default AddressPage;
