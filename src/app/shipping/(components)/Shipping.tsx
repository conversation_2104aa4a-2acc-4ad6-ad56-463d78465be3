"use client";

import { SHOPPING_CART } from "@/constants/nextjsRoutes";
import Link from "next/link";
import ArrowRight from "@/assets/icons/arrow-right.svg";
import { useTranslations } from "next-intl";
import BottomBar from "@/components/ui/bottomBar";
import Button from "@/components/ui/button";
import { thousandSeparator } from "@/utils/number";
import { TOMAN_CURRENCY } from "@/constants/constacts";
import Loading from "@/components/ui/loading";
import { useCartQuery, useCheckoutQuery } from "@/services/apis/cart/cart";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import { useProfileQuery } from "@/services/apis/profile/profile";
import ArrowBlueLeftIcon from "@/assets/icons/arrow-left-blue.svg";
import PlusBlueIcon from "@/assets/icons/plus-blue.svg";
import LocationIcon from "@/assets/icons/location.svg";
import Header from "@/components/ui/header";
import VpnWarning from "@/components/containers/vpnWarning/VpnWarning";
import AddressForm from "./AddressForm";
import ShippingOrders from "./ShippingOrders";
import SelectAddress from "./SelectAddress";
import ShippingInfo from "./shippingInfo";
import PaymentMethod from "./PaymentMethod";

function Shipping() {
  const t = useTranslations();
  const { open, close } = useActionSheet();
  const { data: checkout, refetch: refetchCheckout } = useCheckoutQuery();
  const { data: carts, isLoading } = useCartQuery();
  const { data: profile } = useProfileQuery();

  const selectedAddress = profile?.data?.addresses?.find(item => item?.id === profile?.data?.default_address);

  const handleAddAddress = () => {
    open(<AddressForm />, {
      title: t("shipping.addressForm.addTitle"),
      fitHeight: true,
      bottomClose: true,
      backdropBlur: true,
      events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
    });
  };

  const handleSelectAddress = () => {
    open(<SelectAddress />, {
      title: t("shipping.selectAddress"),
      fitHeight: true,
      bottomClose: true,
      backdropBlur: true,
      events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
    });
  };

  const handlePay = () => {
    if (checkout?.data?.vpn) {
      open(<VpnWarning />, {
        fitHeight: true,
        bottomClose: true,
        backdropBlur: true,

        events: {
          onDidDismiss: () => {
            refetchCheckout();
            close();
          },
          onBackdropTap: () => close()
        }
      });
      return;
    }

    open(<PaymentMethod />, {
      fitHeight: true,
      title: t("checkout.title"),
      bottomClose: true,
      backdropBlur: true,
      events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
    });
  };

  return (
    <div className="flex h-full w-full flex-col overflow-x-hidden">
      <Header
        title={t("shipping.title")}
        startAdornment={
          <Link href={SHOPPING_CART} className="size-6">
            <ArrowRight />
          </Link>
        }
        endAdornment={<div className="size-6" />}
      />

      {isLoading ? (
        <div className="flex h-full w-full items-center justify-center">
          <Loading />
        </div>
      ) : (
        <div className="flex w-full flex-col gap-4 overflow-auto px-4 py-3">
          <div className="flex flex-col gap-3 rounded-lg bg-surface-primary p-4">
            <div className="flex items-center justify-between">
              <span className="text-13 font-medium">{t("shipping.shippingAddress")}</span>

              {selectedAddress ? (
                <div className="flex cursor-pointer items-center gap-1" onClick={handleSelectAddress}>
                  <span className="text-13 font-semibold text-content-on-info">{t("shipping.editAddress")}</span>
                  <ArrowBlueLeftIcon />
                </div>
              ) : (
                <div className="flex cursor-pointer items-center gap-1" onClick={handleAddAddress}>
                  <PlusBlueIcon />
                  <span className="text-13 font-semibold text-content-on-info">{t("shipping.AddAddress")}</span>
                </div>
              )}
            </div>

            {selectedAddress ? (
              <ShippingInfo address={selectedAddress} defaultAddress={profile?.data?.default_address} />
            ) : (
              <div className="flex items-center justify-center rounded-md bg-gray-20 px-3 py-5">
                <div className="flex items-center gap-2">
                  <LocationIcon />
                  <span className="text-xs font-medium">{t("shipping.notSelectedAddress")}</span>
                </div>
              </div>
            )}
          </div>

          <div className="shrink-0 rounded-lg bg-surface-primary p-4">
            <div className="flex items-center justify-between">
              <span className="text-13 font-medium">{t("shipping.orders")}</span>

              <Link prefetch href={SHOPPING_CART} className="flex items-center gap-1">
                <span className="text-13 font-semibold text-content-on-info">{t("shipping.editOrders")}</span>
                <ArrowBlueLeftIcon />
              </Link>
            </div>

            <ShippingOrders carts={carts} />
          </div>

          <div className="flex flex-col gap-4 rounded-lg bg-surface-primary p-4">
            <div className="flex items-center justify-between border-b border-b-gray-40 pb-4">
              <span className="text-xs font-medium text-content-secondary">
                {t("shipping.payItems.totalProductPrice")}
              </span>
              <div className="flex items-center gap-0.5">
                <span className="text-xs font-medium text-content-primary">
                  {thousandSeparator(carts?.data?.sub_total_price)}
                </span>
                <span className="text-10 text-content-tertiary">{TOMAN_CURRENCY}</span>
              </div>
            </div>

            <div className="flex items-center justify-between border-b border-b-gray-40 pb-4">
              <span className="text-xs font-medium text-content-secondary">{t("shipping.payItems.tax")}</span>
              <div className="flex items-center gap-0.5">
                <span className="text-xs font-medium text-content-primary">
                  {thousandSeparator(carts?.data?.total_tax)}
                </span>
                <span className="text-10 text-content-tertiary">{TOMAN_CURRENCY}</span>
              </div>
            </div>

            <div className="flex items-center justify-between border-b border-b-gray-40 pb-4">
              <span className="text-xs font-medium text-content-secondary">{t("shipping.payItems.shippingPrice")}</span>
              <div className="flex items-center gap-0.5">
                <span className="text-xs font-medium text-content-primary">
                  {thousandSeparator(carts?.data?.total_shipping)}
                </span>
                <span className="text-10 text-content-tertiary">{TOMAN_CURRENCY}</span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-13 font-semibold">{t("shipping.payItems.totalPrice")}</span>
              <div className="flex items-center gap-0.5">
                <span className="text-xs font-medium text-content-primary">
                  {thousandSeparator(carts?.data?.total_price)}
                </span>
                <span className="text-10 text-content-tertiary">{TOMAN_CURRENCY}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {!!carts?.data?.total_price && carts?.data?.total_price > 0 && (
        <div className="mt-auto flex flex-col">
          <BottomBar className="border-t-transparent shadow-sm">
            <div className="flex w-full items-center justify-between">
              <div className="flex flex-col gap-0.5">
                <span className="text-10 font-medium text-content-secondary">{t("shipping.totalPrice")}</span>
                <span className="text-base font-bold text-content-primary">
                  {thousandSeparator(carts?.data?.total_price)} <span className="text-10">{TOMAN_CURRENCY}</span>
                </span>
              </div>
              <Button size="lg" disabled={!selectedAddress?.id} onClick={handlePay}>
                {t("shipping.pay")}
              </Button>
            </div>
          </BottomBar>
        </div>
      )}
    </div>
  );
}

export default Shipping;
