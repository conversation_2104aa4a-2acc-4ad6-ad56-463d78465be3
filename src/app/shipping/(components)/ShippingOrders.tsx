import { Keyboard, Mousewheel, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import { ICartResponse } from "@/services/apis/cart/types";
import ShippingOrder from "./ShippingOrder";

interface IShippingOrdersProps {
  carts?: ICartResponse;
}

function ShippingOrders({ carts }: IShippingOrdersProps) {
  return (
    <div className="mt-4">
      <Swiper
        dir="rtl"
        slidesPerView="auto"
        keyboard
        grabCursor
        spaceBetween={36}
        modules={[Navigation, Keyboard, Mousewheel]}
      >
        {carts?.data?.line_items?.map(item => (
          <SwiperSlide key={item?.variant_id} className="!h-auto max-w-[104px] first:mr-3 last:ml-4">
            <ShippingOrder cart={item} />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
}

export default ShippingOrders;
