"use client";

import { useTranslations } from "next-intl";
import { useCheckoutQuery } from "@/services/apis/cart/cart";
import Button from "@/components/ui/button";
import { useState } from "react";
import { twMerge } from "tailwind-merge";
import Loading from "@/components/ui/loading";
import { useOrderPlaceMutation } from "@/services/apis/order/order";
import { PAYMENT_STATUS } from "@/constants/nextjsRoutes";
import { handleErrorResponse } from "@/utils/handleError";
import ActiveIcon from "@/assets/icons/active-icon.svg";
import NotActiveIcon from "@/assets/icons/not-active-icon.svg";
import { setCookie } from "@/hooks/useCookie";
import { ORDER_ID } from "@/constants/cookies";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";

function PaymentMethod() {
  const t = useTranslations();
  const { close } = useActionSheet();
  const [paymentMethod, setPaymentMethod] = useState("");

  const { data: checkout, isLoading } = useCheckoutQuery();
  const { mutate: orderPlaceMutate, isPending: isLoadingPay } = useOrderPlaceMutation();

  const onPay = async () => {
    try {
      await orderPlaceMutate(
        { callback_url: PAYMENT_STATUS, payment_method: paymentMethod as string },
        {
          onSuccess: data => {
            if (data?.data?.data?.url) {
              close();
              window.location.assign(data?.data?.data?.url);
              setCookie(ORDER_ID, data?.data?.data?.id);
            }
          },
          onError: error => {
            if (error) handleErrorResponse({ error, t });
          }
        }
      );
    } catch (error: any) {
      if (error) handleErrorResponse({ error, t });
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <Loading />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-2 p-6">
      {checkout?.data?.payment_methods?.map(item => (
        <div
          key={item?.id}
          className={twMerge(
            "border-secondary flex cursor-pointer items-center justify-between rounded-lg border p-4",
            item?.key === paymentMethod ? "border-info bg-surface-info" : ""
          )}
          onClick={() => setPaymentMethod(item?.key)}
        >
          <div className="flex items-center gap-2">
            {item?.logo && <img src={item?.logo} alt={item?.name} className="size-6 object-contain" />}
            <span className="ont-medium text-sm">{item?.name}</span>
          </div>

          {item?.key === paymentMethod ? <ActiveIcon /> : <NotActiveIcon />}
        </div>
      ))}

      <Button onClick={onPay} isLoading={isLoadingPay} disabled={isLoadingPay || !paymentMethod} className="mt-4">
        {t("checkout.confirmAndContinue")}
      </Button>
    </div>
  );
}

export default PaymentMethod;
