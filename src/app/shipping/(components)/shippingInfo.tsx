import TickBlueIcon from "@/assets/icons/tick-blue.svg";
import EmptyCircleIcon from "@/assets/icons/empty-circle.svg";
import EditIcon from "@/assets/icons/edit-icon.svg";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import { Address } from "@/services/apis/profile/types";
import { useTranslations } from "next-intl";
import { useSelectAddressMutation } from "@/services/apis/profile/profile";
import { handleErrorResponse } from "@/utils/handleError";
import Loading from "@/components/ui/loading";
import AddressForm from "./AddressForm";

interface IShippingInfoProps {
  address?: Address;
  defaultAddress?: string;
  hasEdit?: boolean;
  isProfile?: boolean;
}

function ShippingInfo({ address, defaultAddress, hasEdit, isProfile }: IShippingInfoProps) {
  const t = useTranslations();
  const { open, close } = useActionSheet();

  const selectedAddress = address?.id === defaultAddress;

  const { mutate: selectAddressMutate, isPending: isSelectAddressLoading } = useSelectAddressMutation();

  const onSelectAddress = async () => {
    try {
      await selectAddressMutate(
        { id: address?.id || "" },
        {
          onError: error => {
            handleErrorResponse({ error, t });
          }
        }
      );
    } catch (error: any) {
      handleErrorResponse({ error, t });
    }
  };

  const handleEditAddress = (e: any) => {
    e.stopPropagation();
    if (isProfile) {
      open(
        <AddressForm
          isEdit
          id={address?.id}
          address={address?.address1}
          contact_number={address?.phone_number}
          first_name={address?.first_name}
          last_name={address?.last_name}
          location_id={address?.location_id}
          zip={address?.zip}
        />,
        {
          title: t("shipping.addressForm.editAddress"),
          fitHeight: true,
          bottomClose: true,
          backdropBlur: true,
          events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
        }
      );
      return;
    }
    close()?.then(() => {
      open(
        <AddressForm
          isEdit
          id={address?.id}
          address={address?.address1}
          contact_number={address?.phone_number}
          first_name={address?.first_name}
          last_name={address?.last_name}
          location_id={address?.location_id}
          zip={address?.zip}
        />,
        {
          title: t("shipping.addressForm.editAddress"),
          fitHeight: true,
          bottomClose: true,
          backdropBlur: true,
          events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
        }
      );
    });
  };

  return (
    <div className="flex h-full cursor-pointer gap-2 rounded-md bg-gray-20 p-3" onClick={onSelectAddress}>
      <div className="flex flex-col items-center gap-2">
        {isSelectAddressLoading ? (
          <Loading size="xs" className="size-6" />
        ) : selectedAddress ? (
          <TickBlueIcon />
        ) : (
          <EmptyCircleIcon className="cursor-pointer" />
        )}
        <div className="h-full w-[1px] flex-1 bg-border-primary" />
      </div>

      <div className="flex w-full flex-col gap-2">
        <div className="flex items-center justify-between">
          <p className="text-sm font-medium">
            {address?.state} ، {address?.city}
          </p>

          {!!hasEdit && <EditIcon className="cursor-pointer" onClick={handleEditAddress} />}
        </div>
        <p className="text-13">{address?.address1}</p>

        <div className="flex items-center justify-between">
          <span className="text-xs text-content-tertiary">{t("shipping.name")}</span>
          <span className="text-xs font-medium">
            {address?.first_name || ""} {address?.last_name}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-xs text-content-tertiary">{t("shipping.phoneNumber")}</span>
          <span className="text-xs font-medium">{address?.phone_number}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs text-content-tertiary">{t("shipping.zipCode")}</span>
          <span className="text-xs font-medium">{address?.zip}</span>
        </div>
      </div>
    </div>
  );
}

export default ShippingInfo;
