import { TOMAN_CURRENCY } from "@/constants/constacts";
import { useCartModification } from "@/hooks/useCartModification";
import { ICartData } from "@/services/apis/cart/types";
import { thousandSeparator } from "@/utils/number";
import Image from "next/image";
import { useState, useEffect } from "react";
import Link from "next/link";
import { PRODUCT } from "@/constants/nextjsRoutes";
import CartModification from "@/components/ui/cartModification/CartModification";

interface IShippingOrderProps {
  cart: ICartData["line_items"][number];
}

function ShippingOrder({ cart }: IShippingOrderProps) {
  const [cartCount, setCartCount] = useState(0);

  const { handleAddProduct, handleRemoveProduct } = useCartModification({
    singleVariant: cart as any,
    onChangeCartCount: setCartCount
  });

  useEffect(() => {
    if (cart?.variant_id) setCartCount(cart?.quantity);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cart?.quantity]);

  return (
    <Link prefetch href={`${PRODUCT}/${cart?.product_id}`} className="flex h-full flex-col justify-between gap-3">
      <div className="flex flex-col items-center gap-3">
        <div className="relative size-20">
          <Image src={cart?.cover?.url} alt={cart?.cover?.alt} fill className="max-h-20 object-contain" />
        </div>

        <div className="flex items-center gap-0.5">
          <span className="text-xs font-medium text-content-primary">{thousandSeparator(cart?.price)}</span>
          <span className="text-10 text-content-tertiary">{TOMAN_CURRENCY}</span>
        </div>
      </div>

      {cartCount > 0 && (
        <CartModification
          cartCount={cartCount}
          onMinimize={value => {
            setCartCount(value);
            handleRemoveProduct(value);
          }}
          onPlus={value => {
            setCartCount(value);
            handleAddProduct(value);
          }}
        />
      )}
    </Link>
  );
}

export default ShippingOrder;
