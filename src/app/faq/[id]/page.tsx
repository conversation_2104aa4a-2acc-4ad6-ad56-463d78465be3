import { STORE_HANDLE } from "@/constants/cookies";
import { generalMetaTags } from "@/utils/generateGeneralMetaTags";
import { getData } from "@/utils/getServerCookie";
import FAQ from "../(components)/Faq";

export async function generateMetadata() {
  const storeHandle = await getData(STORE_HANDLE);
  return generalMetaTags({ pageName: "سوالات متداول", storeHandle });
}

function FAQPage() {
  return <FAQ />;
}

export default FAQPage;
