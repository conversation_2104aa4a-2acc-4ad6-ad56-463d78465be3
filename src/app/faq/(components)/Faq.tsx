"use client";

import { HOME } from "@/constants/nextjsRoutes";
import Link from "next/link";
import ArrowR<PERSON> from "@/assets/icons/arrow-right.svg";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import { faqItems } from "@/app/(home)/(components)/utils";
import Header from "@/components/ui/header";
import { Swiper, SwiperSlide } from "swiper/react";
import { Keyboard, Mousewheel, Navigation } from "swiper/modules";
import FaqCollapsable from "./FaqCollapsable";

function FAQ() {
  const params = useParams();
  const id = params?.id;
  const t = useTranslations();
  const contents = faqItems?.find(item => item.type === id)?.children;

  return (
    <div className="flex h-full w-full flex-col">
      <div className="flex flex-col bg-surface-primary py-3">
        <div className="">
          <Header
            title={t("faq.title")}
            startAdornment={
              <Link prefetch href={HOME} className="size-6">
                <ArrowRight />
              </Link>
            }
            endAdornment={<div className="size-6" />}
          />

          <div className="mt-4 border-t border-t-border-secondary px-4 py-2">
            <Swiper
              dir="rtl"
              slidesPerView="auto"
              keyboard
              grabCursor
              spaceBetween={12}
              modules={[Navigation, Keyboard, Mousewheel]}
            >
              {faqItems?.map(item => (
                <SwiperSlide key={item?.id} className="max-w-fit">
                  <Link
                    href={`${item.path}/${item?.type}`}
                    className={`flex items-center gap-2 whitespace-nowrap rounded-full border px-3 py-2 text-xs font-medium ${
                      id === item.type
                        ? "border-border-inverse text-content-primary"
                        : "border-border-secondary text-content-secondary"
                    }`}
                  >
                    {t(`home.footer.${item.type as keyof IntlMessages["home"]["footer"]}`)}
                  </Link>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      </div>

      <div className="w-full flex-1 overflow-auto p-4">
        <div className="h-full rounded-lg bg-surface-primary">
          <div className="flex h-full flex-1 flex-col gap-4">
            {!!contents?.length && (
              <div className="flex flex-col gap-3 p-4">
                {contents?.map(item => (
                  <FaqCollapsable
                    key={item.id}
                    title={t(`home.footer.${item.title as keyof IntlMessages["home"]["footer"]}`)}
                    description={item?.content}
                  />
                ))}
              </div>
            )}

            {!contents?.length && (
              <div className="flex h-full items-center justify-center">
                <div className="px-3 py-6 text-center text-content-secondary">{t("messages.noMessage")}</div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default FAQ;
