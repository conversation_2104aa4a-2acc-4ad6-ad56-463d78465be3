/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import ArrowLeft01RoundIcon from "@/assets/icons/arrow-left-01-round.svg";
import { useState } from "react";
import { twMerge } from "tailwind-merge";

function FaqCollapsable({ title, description }: { title: string; description?: string }) {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <div
      className={twMerge(
        "flex flex-col rounded-lg border border-border-primary px-5 py-4 transition-all",
        isOpen ? "!border-content-on-info-2 !bg-surface-info" : ""
      )}
    >
      <div className="flex cursor-pointer items-center gap-2" onClick={() => setIsOpen(prev => !prev)}>
        <div className="flex flex-1 flex-col gap-1">
          <div className="text-13 font-medium text-content-primary">{title}</div>
        </div>
        <div>
          <ArrowLeft01RoundIcon className={twMerge("transition-transform", isOpen ? "rotate-90" : "-rotate-90")} />
        </div>
      </div>

      <div className={twMerge("mt-4 text-13 font-medium leading-7 text-content-secondary", isOpen ? "" : "hidden")}>
        {description}
      </div>
    </div>
  );
}

export default FaqCollapsable;
