"use client";

import ArrowLeft01RoundIcon from "@/assets/icons/arrow-left-01-round.svg";
import ArrowRightIcon from "@/assets/icons/arrow-right.svg";
import CustomerSupportIcon from "@/assets/icons/customer-support.svg";
import DollarCircleIcon from "@/assets/icons/dollar-circle.svg";
import FlipRightIcon from "@/assets/icons/flip-right.svg";
import InformationDiamondIcon from "@/assets/icons/information-diamond.svg";
import LanguageCircleIcon from "@/assets/icons/language-circle.svg";
import Header from "@/components/ui/header";
import Loading from "@/components/ui/loading";
import useCookie from "@/hooks/useCookie";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import MenuItem from "./(components)/MenuItem";
import MenuItemsBox from "./(components)/MenuItemsBox";

function Settings() {
  const t = useTranslations();
  const router = useRouter();
  const { setCookie, getCookie } = useCookie();
  const theme = getCookie("theme");
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleChangeTheme = (themeName: string) => {
    setCookie("theme", themeName);
    window?.location?.reload();
  };

  return (
    <div className="flex h-full flex-col gap-5 bg-background-secondary">
      <Header title={t("settings.pageTitle")} startAdornment={<ArrowRightIcon onClick={() => router.back()} />} />

      <div className="flex flex-1 flex-col gap-4 px-6">
        <MenuItemsBox>
          <MenuItem
            title={t("settings.language")}
            startAdornment={<LanguageCircleIcon />}
            endAdornment={<div className="px-1 text-content-primary">{t("languages.fa")}</div>}
          />
          <MenuItem
            title={t("settings.appearance")}
            startAdornment={<FlipRightIcon />}
            endAdornment={
              !isMounted ? (
                <Loading size="xs" />
              ) : (
                <div className="px-1 text-content-primary">
                  <select
                    name="theme"
                    id="theme"
                    onChange={e => {
                      handleChangeTheme(e.target.value);
                    }}
                    className="bg-surface-primary outline-none focus:border-blue-500 focus:ring-blue-500"
                    // className="bg-transparent placeholder-surface-primary outline-none"
                    value={theme === "dark" ? "dark" : "light"}
                  >
                    <option value="light">{t("settings.appearance-light")}</option>
                    <option value="dark">{t("settings.appearance-dark")}</option>
                  </select>
                </div>
              )
            }
          />

          <MenuItem
            title={t("settings.currency")}
            startAdornment={<DollarCircleIcon />}
            endAdornment={<div className="px-1 text-content-primary">{t("currency.toman")}</div>}
          />
        </MenuItemsBox>
        <MenuItemsBox>
          <MenuItem
            title={t("settings.support")}
            startAdornment={<CustomerSupportIcon />}
            endAdornment={
              <div className="p-1">
                <ArrowLeft01RoundIcon />
              </div>
            }
          />
          <MenuItem
            title={t("settings.aboutus")}
            startAdornment={<InformationDiamondIcon />}
            endAdornment={
              <div className="p-1">
                <ArrowLeft01RoundIcon />
              </div>
            }
          />
        </MenuItemsBox>
      </div>
    </div>
  );
}

export default Settings;
