import { ReactNode } from "react";

function MenuItem({
  title,
  startAdornment,
  endAdornment
}: {
  title: string;
  startAdornment?: ReactNode;
  endAdornment?: ReactNode;
}) {
  return (
    <div className="flex gap-2 px-3 py-[18px]">
      <div className="h-5 w-5">{startAdornment}</div>
      <div className="flex-1 text-[15px] font-normal text-content-primary">{title}</div>
      <div className="h-5 w-fit">{endAdornment}</div>
    </div>
  );
}

export default MenuItem;
