/* eslint-disable jsx-a11y/html-has-lang */

"use client";

import ErrorPage from "@/components/containers/errorPage/ErrorPage";
import { IRANYekanXFaNumPro } from "@/providers/fonts";

export default function GlobalError() {
  return (
    <html>
      <body
        className={`${IRANYekanXFaNumPro.variable} !font-iran-yekan antialiased`}
        style={{
          background: "rgba(243, 244, 246, 1)"
        }}
      >
        <ErrorPage />
      </body>
    </html>
  );
}
