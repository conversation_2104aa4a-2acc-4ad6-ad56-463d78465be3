import type { MetadataRoute } from "next";
import { STORE } from "@/constants/apiRoutes";
import { apiService } from "@/services/apiService";

export default async function manifest(): Promise<MetadataRoute.Manifest> {
  let store;
  try {
    const response = await apiService.get(STORE);
    store = response.data;
  } catch (e) {
    store = { data: {} };
  }

  return {
    name: store?.data?.name || "Shop Builder",
    short_name: store?.data?.name || "Shop Builder",
    description: store?.data?.description || " description",
    start_url: "/",
    display: "standalone",
    background_color: "#ffffff",
    theme_color: "#000000",
    icons: [
      {
        src: store?.data?.logo || "/icon-192x192.png",
        sizes: "192x192",
        type: "image/png"
      },
      {
        src: store?.data?.logo || "/icon-512x512.jpg",
        sizes: "512x512",
        type: "image/png"
      }
    ]
  };
}
