"use client";

import { HOME, SHIPPING } from "@/constants/nextjsRoutes";
import Link from "next/link";
import ArrowRight from "@/assets/icons/arrow-right.svg";
import { useTranslations } from "next-intl";
// import Trash from "@/assets/icons/trash-big.svg";
import NoCart from "@/assets/icons/no-cart.svg";
import BottomNavBar from "@/components/ui/bottomNavBar";
import BottomBar from "@/components/ui/bottomBar";
import Button from "@/components/ui/button";
import { thousandSeparator } from "@/utils/number";
import { TOMAN_CURRENCY } from "@/constants/constacts";
import Loading from "@/components/ui/loading";
import { useCartQuery } from "@/services/apis/cart/cart";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import useCookie from "@/hooks/useCookie";
import { ACCESS_TOKEN } from "@/constants/cookies";
import { useRouter } from "next/navigation";
import Header from "@/components/ui/header";
import Auth from "@/components/containers/auth/Auth";
import CartItem from "./CartItem";

function Cart() {
  const t = useTranslations();
  const router = useRouter();
  const { getCookie } = useCookie();
  const { open, close } = useActionSheet();

  const { data: carts, isLoading, isError } = useCartQuery();

  // const { mutate: removeAll, isPending: isLoadingRemove } = useRemoveAllProductMutation();
  // const handleRemoveAll = async () => {
  //   await removeAll({} as any, {
  //     onSuccess: () => {
  //       close();
  //     },
  //     onError: error => {
  //       if (error) {
  //         clientDefaultErrorHandler({ error, t });
  //       }
  //     }
  //   });
  // };

  // const onRemoveAll = () => {
  //   open(
  //     <div className="flex w-full flex-col items-center justify-center px-6 pb-6">
  //       <RemoveRedTrash />
  //       <p className="mt-5 text-base font-bold">{t("cart.removeCartTitle")}</p>
  //       <p className="mt-2 text-xs font-medium text-content-tertiary">{t("cart.removeCartSubtitle")}</p>

  //       <div className="mt-6 flex w-full flex-col gap-4">
  //         <Button
  //           size="lg"
  //           className="w-full bg-content-on-error-2"
  //           onClick={handleRemoveAll}
  //           isLoading={isLoadingRemove}
  //           disabled={isLoadingRemove}
  //         >
  //           {t("cart.confirm")}
  //         </Button>
  //         <Button size="lg" variant="outline" className="w-full text-content-secondary" onClick={close}>
  //           {t("cart.cancel")}
  //         </Button>
  //       </div>
  //     </div>,
  //     {
  //       fitHeight: true,
  //       bottomClose: true,
  //       backdropBlur: true,
  //       events: {
  //         onDidDismiss: () => close(),
  //         onBackdropTap: () => close()
  //       }
  //     }
  //   );
  // };

  const handleCompleteOrder = () => {
    const accessToken = getCookie(ACCESS_TOKEN);
    if (accessToken) {
      router.push(SHIPPING);
    } else {
      open(
        <div>
          <Auth onSuccess={() => router.push(SHIPPING)} />
        </div>,
        {
          closable: false,
          fitHeight: true,
          bottomClose: true,
          backdropBlur: true,
          events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
        }
      );
    }
  };

  return (
    <div className="flex h-full w-full flex-col">
      <Header
        title={t("cart.title")}
        startAdornment={
          <Link prefetch href={HOME} className="size-6">
            <ArrowRight />
          </Link>
        }
        endAdornment={<div className="size-6" />}
        // endAdornment={<Trash className="cursor-pointer" onClick={onRemoveAll} />}
      />

      <div className="w-full flex-1 overflow-auto px-4 py-3">
        {isLoading ? (
          <div className="flex h-full w-full items-center justify-center">
            <Loading />
          </div>
        ) : !carts?.data?.line_items?.length || isError ? (
          <div className="flex h-full flex-col items-center justify-center">
            <NoCart />
            <p className="mt-4 text-15 font-bold text-content-primary">{t("cart.noCartTitle")}</p>
            <p className="mt-2 text-xs text-content-tertiary">{t("cart.noCartSubtitle")}</p>
          </div>
        ) : (
          <div className="flex flex-col gap-4">
            {carts?.data?.line_items?.map(item => (
              <CartItem key={item?.variant_id} item={item} />
            ))}
          </div>
        )}
      </div>

      <div className="mt-auto flex flex-col">
        {!isError && !!carts?.data?.total_price && carts?.data?.total_price > 0 && (
          <BottomBar className="border-b border-b-border-secondary border-t-transparent shadow-sm">
            <div className="flex w-full items-center justify-between">
              <div className="flex flex-col gap-0.5">
                <span className="text-10 font-medium text-content-secondary">{t("cart.totalPrice")}</span>
                <span className="text-base font-bold text-content-primary">
                  {thousandSeparator(carts?.data?.total_price)} <span className="text-10">{TOMAN_CURRENCY}</span>
                </span>
              </div>
              <Button size="lg" onClick={handleCompleteOrder}>
                {t("cart.completeOrder")}
              </Button>
            </div>
          </BottomBar>
        )}

        <BottomNavBar />
      </div>
    </div>
  );
}

export default Cart;
