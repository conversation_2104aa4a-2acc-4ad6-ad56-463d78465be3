import PriceDisplay from "@/components/ui/priceDisplay/PriceDisplay";
import Image from "next/image";
import { ICartData } from "@/services/apis/cart/types";
import { calculateDiscount } from "@/app/(home)/(components)/utils";
import { useState, useEffect } from "react";
import { useCartModification } from "@/hooks/useCartModification";
import Link from "next/link";
import { PRODUCT } from "@/constants/nextjsRoutes";
import CartModification from "@/components/ui/cartModification/CartModification";

interface ICartItemProps {
  item: ICartData["line_items"][number];
}

function CartItem({ item }: ICartItemProps) {
  const variantValues = Object.values(item?.options || {});

  const [cartCount, setCartCount] = useState(0);

  const { handleAddProduct, handleRemoveProduct } = useCartModification({
    singleVariant: item as any,
    onChangeCartCount: setCartCount
  });

  useEffect(() => {
    if (item?.variant_id) setCartCount(item?.quantity);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [item?.quantity]);

  return (
    <Link prefetch href={`${PRODUCT}/${item?.product_id}`} className="rounded-lg bg-surface-primary p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-start gap-4">
          {!!item?.cover?.url && (
            <div className="relative flex size-[64px] items-center justify-center">
              <Image fill src={item?.cover?.url || ""} alt={item?.cover?.alt} className="object-contain" />
            </div>
          )}
          <div className="flex flex-col gap-2">
            <h3 className="text-xs font-medium leading-[18px]">{item.name}</h3>
            <div className="flex items-center gap-2 text-xs text-content-secondary">
              {variantValues?.map(val => (
                <p className="border-l border-l-border-primary pl-2 last:border-l-transparent">{val}</p>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="mt-3 flex items-center justify-between">
        {cartCount > 0 && (
          <CartModification
            className="!h-10"
            cartCount={cartCount}
            onMinimize={value => {
              setCartCount(value);
              handleRemoveProduct(value);
            }}
            onPlus={value => {
              setCartCount(value);
              handleAddProduct(value);
            }}
          />
        )}

        <PriceDisplay
          finalPrice={item?.price}
          percentageClassName="flex-row-reverse"
          finalPriceClassName="!text-left w-full text-base font-semibold"
          originalPrice={item?.compare_at_price}
          discountPercentage={calculateDiscount(item?.price, item?.compare_at_price) ?? 0}
        />
      </div>
    </Link>
  );
}

export default CartItem;
