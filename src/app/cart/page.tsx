import { STORE_HANDLE } from "@/constants/cookies";
import { generalMetaTags } from "@/utils/generateGeneralMetaTags";
import { getData } from "@/utils/getServerCookie";
import Cart from "./(components)/Cart";

export async function generateMetadata() {
  const storeHandle = await getData(STORE_HANDLE);
  return generalMetaTags({ pageName: "سبد خرید", storeHandle });
}

function CartPage() {
  return <Cart />;
}

export default CartPage;
