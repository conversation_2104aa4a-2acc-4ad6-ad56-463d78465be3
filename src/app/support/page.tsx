import Support from "@/components/containers/support/Support";
import { STORE_HANDLE } from "@/constants/cookies";
import { generalMetaTags } from "@/utils/generateGeneralMetaTags";
import { getData } from "@/utils/getServerCookie";

export async function generateMetadata() {
  const storeHandle = await getData(STORE_HANDLE);
  return generalMetaTags({ pageName: "پشتیبانی", storeHandle });
}

function SupportPage() {
  return <Support />;
}

export default SupportPage;
