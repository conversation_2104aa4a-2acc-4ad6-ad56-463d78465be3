import Button from "@/components/ui/button";
import { ORDER } from "@/constants/nextjsRoutes";
import PaymentSuccessIcon from "@/assets/icons/payment-success.svg";
import { useTranslations } from "next-intl";
import { dateConverter } from "@/utils/date";
import { IOrderResponse } from "@/services/apis/order/types";
import { useRouter } from "next/navigation";
import { getCookie, removeCookie } from "@/hooks/useCookie";
import { CART_DATA, ORDER_ID } from "@/constants/cookies";
import { useEffect } from "react";

interface IPaymentSuccessProps {
  order?: IOrderResponse;
}

function PaymentSuccess({ order }: IPaymentSuccessProps) {
  const t = useTranslations();
  const router = useRouter();

  useEffect(() => {
    const cartData = getCookie(CART_DATA);
    if (cartData) {
      removeCookie(CART_DATA);
    }
  }, []);

  return (
    <div className="flex h-full w-full items-center justify-center p-4">
      <div className="w-full rounded-lg bg-surface-primary p-6">
        <div className="flex flex-col items-center justify-center gap-2">
          <PaymentSuccessIcon />
          <span className="text-15 font-semibold text-content-primary">{t("paymentStatus.success.title")}</span>
        </div>

        <div className="mt-8 flex flex-col rounded-lg bg-surface-secondary p-4">
          <div className="flex items-center justify-between border-b border-b-border-primary pb-4">
            <span className="text-xs font-medium text-content-secondary">{t("paymentStatus.success.orderCode")}</span>
            <span className="text-xs font-medium text-content-primary">{order?.data?.number}</span>
          </div>
          {order?.data?.created_at && (
            <div className="flex items-center justify-between border-b border-b-border-primary py-4">
              <span className="text-xs font-medium text-content-secondary">{t("paymentStatus.success.date")}</span>
              <div className="flex items-center gap-0.5">
                <span className="text-xs font-medium text-content-primary">
                  {dateConverter(order?.data?.created_at, { format: "HH:mm" })}
                </span>
                <div className="h-full w-[1px] bg-content-primary" />
                <span className="text-xs font-medium text-content-primary">
                  {dateConverter(order?.data?.created_at, { format: "YYYY" })}.
                  {dateConverter(order?.data?.created_at, { format: "MM" })}.
                  {dateConverter(order?.data?.created_at, { format: "DD" })}
                </span>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between pt-4">
            <span className="text-xs font-medium text-content-secondary">{t("paymentStatus.success.trackCode")}</span>
            <span className="text-xs font-medium text-content-primary">{order?.data?.transaction?.tracking_code}</span>
          </div>
        </div>

        <Button
          className="mt-6 w-full"
          size="lg"
          onClick={() => {
            router.replace(ORDER);
            removeCookie(ORDER_ID);
          }}
        >
          {t("general.myOrders")}
        </Button>
      </div>
    </div>
  );
}

export default PaymentSuccess;
