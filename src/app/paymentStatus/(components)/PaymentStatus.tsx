"use client";

import { useSearchParams } from "next/navigation";
import { getCookie } from "@/hooks/useCookie";
import { ORDER_ID } from "@/constants/cookies";
import { useOrderQuery } from "@/services/apis/order/order";
import Loading from "@/components/ui/loading";
import PaymentSuccess from "./PaymentSuccess";
import PaymentFailure from "./PaymentFailure";

function PaymentStatus() {
  const searchParams = useSearchParams();
  const paymentStatus = searchParams.get("payment_status");
  const orderId = getCookie(ORDER_ID) as string;

  const { data: order, isLoading: isLoadingOrder } = useOrderQuery({ id: orderId });

  if (isLoadingOrder) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <Loading />
      </div>
    );
  }

  if (paymentStatus === "success") {
    return <PaymentSuccess order={order} />;
  }

  if (paymentStatus === "failed") {
    return <PaymentFailure />;
  }

  return null;
}

export default PaymentStatus;
