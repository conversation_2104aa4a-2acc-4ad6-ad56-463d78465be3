import Button from "@/components/ui/button";
import { SHIPPING } from "@/constants/nextjsRoutes";
import PaymentFailureIcon from "@/assets/icons/payment-fail.svg";

import { useTranslations } from "next-intl";
import Link from "next/link";

function PaymentFailure() {
  const t = useTranslations();

  return (
    <div className="flex h-full w-full items-center justify-center p-4">
      <div className="w-full rounded-lg bg-surface-primary p-6">
        <div className="flex flex-col items-center justify-center gap-2">
          <PaymentFailureIcon />
          <span className="text-base font-bold text-content-primary">{t("paymentStatus.failure.title")}</span>
        </div>

        <div className="mt-8 rounded-lg bg-surface-secondary p-4">
          <span className="text-13">{t("paymentStatus.failure.subtitle")}</span>
        </div>

        <Link prefetch href={SHIPPING}>
          <Button className="mt-8 w-full" size="lg">
            {t("paymentStatus.failure.retryText")}
          </Button>
        </Link>
      </div>
    </div>
  );
}

export default PaymentFailure;
