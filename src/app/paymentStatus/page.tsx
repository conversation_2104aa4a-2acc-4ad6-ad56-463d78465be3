import { STORE_HANDLE } from "@/constants/cookies";
import { generalMetaTags } from "@/utils/generateGeneralMetaTags";
import { getData } from "@/utils/getServerCookie";
import PaymentStatus from "./(components)/PaymentStatus";

export async function generateMetadata() {
  const storeHandle = await getData(STORE_HANDLE);
  return generalMetaTags({ pageName: "وضعیت پرداخت", storeHandle });
}

function PaymentStatusPage() {
  return <PaymentStatus />;
}

export default PaymentStatusPage;
