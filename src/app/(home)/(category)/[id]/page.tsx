import { STORE_HANDLE } from "@/constants/cookies";
import { getData } from "@/utils/getServerCookie";
import { generateCategoryMetaTags } from "@/utils/generateCategoryMetaTags";
import Products from "@/app/(home)/(components)/Products";

export async function generateMetadata({ params }: { params: { id: string } }) {
  const storeHandle = await getData(STORE_HANDLE);
  return generateCategoryMetaTags({ params, storeHandle });
}

export default function ProductPage() {
  return <Products />;
}
