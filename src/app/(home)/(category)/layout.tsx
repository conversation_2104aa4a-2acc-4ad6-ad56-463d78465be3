"use client";

import ArrowRight from "@/assets/icons/arrow-right.svg";
import Magnifier from "@/assets/icons/Magnifier.svg";
import Link from "next/link";
import { HOME, SEARCH, SHOPPING_CART } from "@/constants/nextjsRoutes";
import CartBadge from "@/components/ui/cartBadge/cartBadge";
import Categories from "@/app/(home)/(components)/Categories";

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <div className="flex h-full w-full flex-col">
      <div className="flex flex-col bg-surface-primary pt-3">
        <div className="flex h-14 items-center justify-between p-4">
          <Link prefetch href={HOME}>
            <ArrowRight />
          </Link>

          <div className="flex items-center gap-3.5">
            <Link prefetch href={SEARCH}>
              <Magnifier />
            </Link>
            <Link prefetch href={SHOPPING_CART}>
              <CartBadge />
            </Link>
          </div>
        </div>

        <div className="border-b border-b-border-secondary py-3">
          <Categories />
        </div>
      </div>

      {children}
    </div>
  );
}
