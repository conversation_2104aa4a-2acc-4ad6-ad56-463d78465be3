"use client";

import SearchIcon from "@/assets/icons/search.svg";
import Input from "@/components/ui/input";
import InputWrapper from "@/components/ui/inputWrapper";
import { SEARCH } from "@/constants/nextjsRoutes";
import { useTranslations } from "next-intl";
import Link from "next/link";

function SearchBar() {
  const t = useTranslations();

  return (
    <div className="flex w-full shrink-0 items-center gap-3">
      <Link prefetch href={SEARCH} className="flex-1">
        <InputWrapper variant="filled" rounded="lg" startAdornment={<SearchIcon className="size-5 shrink-0" />}>
          <Input placeholder={t("general.searchWithDots")} />
        </InputWrapper>
      </Link>
    </div>
  );
}

export default SearchBar;
