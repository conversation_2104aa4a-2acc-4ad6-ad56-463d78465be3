/* eslint-disable jsx-a11y/anchor-is-valid */

import PriceDisplay from "@/components/ui/priceDisplay/PriceDisplay";
import Link from "next/link";
import { PRODUCT } from "@/constants/nextjsRoutes";
import { Variant } from "@/services/apis/product/types";
import Image from "next/image";
import { useParams } from "next/navigation";
import AddToCart from "./AddToCart";

interface IProductProps {
  id?: string;
  image: string;
  title: string;
  finalPrice: number;
  discountPercentage: number;
  originalPrice: number;
  hasVariant?: boolean;
  variants: Variant[];
}

function Product({
  image,
  title,
  discountPercentage,
  finalPrice,
  originalPrice,
  id,
  hasVariant,
  variants
}: IProductProps) {
  const params = useParams();
  const categoryId = params?.id as string;

  return (
    <Link prefetch href={`${PRODUCT}/${id}?categoryId=${categoryId}`} legacyBehavior>
      <a className="flex flex-col rounded-lg bg-surface-primary p-2">
        <div className="relative flex aspect-square items-center justify-center rounded-lg bg-gray-40">
          <Image src={image} alt={title} fill className="rounded-lg object-contain" />

          {!hasVariant && (
            <div className="absolute bottom-2 right-2">
              <AddToCart variants={variants} />
            </div>
          )}
        </div>

        <div className="h-12">
          <p className="mt-3 line-clamp-2 text-xs font-medium leading-[24px] text-content-primary">{title}</p>
        </div>

        <div className="mt-5">
          <PriceDisplay finalPrice={finalPrice} discountPercentage={discountPercentage} originalPrice={originalPrice} />
        </div>
      </a>
    </Link>
  );
}

export default Product;
