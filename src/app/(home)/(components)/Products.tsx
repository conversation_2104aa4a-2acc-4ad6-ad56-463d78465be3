"use client";

import ArrowRight from "@/assets/icons/arrow-right.svg";
import Magnifier from "@/assets/icons/Magnifier.svg";
import Link from "next/link";
import { HOME, SEARCH, SHOPPING_CART } from "@/constants/nextjsRoutes";
import { useProductsInfiniteQuery } from "@/services/apis/product/product";
import { InView } from "react-intersection-observer";
import Loading from "@/components/ui/loading";
import { useParams, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import CartBadge from "@/components/ui/cartBadge/cartBadge";
import { calculateDiscount } from "./utils";
import Filters from "./Filters";
import Product from "./Product";
import Categories from "./Categories";

export default function Products() {
  const t = useTranslations();
  const params = useParams();
  const searchParams = useSearchParams();
  const subCategoryId = searchParams.get("subCategoryId");
  const categoryId = params?.id as string;

  const {
    data: products,
    isLoading,
    isSuccess,
    isFetching,
    hasNextPage,
    fetchNextPage
  } = useProductsInfiniteQuery({ category_id: subCategoryId || categoryId, page_size: 50 });

  return (
    <div className="flex flex-1 flex-col">
      <div className="bg-surface-primary pb-3 pt-3">
        <Filters products={products} />
      </div>
      <div className="flex-1">
        {isLoading ? (
          <div className="flex h-full items-center justify-center">
            <Loading />
          </div>
        ) : isSuccess && !products?.pages[0]?.data?.products?.length ? (
          <div className="flex h-full w-full flex-1 items-center justify-center bg-surface-primary">
            <span className="text-sm text-content-secondary">{t("home.noProducts")}</span>
          </div>
        ) : (
          <div className="h-full overflow-auto p-3.5 px-4">
            <div className="grid grid-cols-2 gap-4">
              {products?.pages?.map(pageItems =>
                pageItems?.data?.products?.map(item => (
                  <Product
                    id={item?.id}
                    discountPercentage={
                      calculateDiscount(item?.cheapest_variant?.price, item?.cheapest_variant?.compare_at_price) ?? 0
                    }
                    variants={item?.variants}
                    hasVariant={item?.has_variant}
                    finalPrice={item?.cheapest_variant?.price}
                    image={item?.cover?.url}
                    originalPrice={item?.cheapest_variant?.compare_at_price}
                    title={item?.name}
                  />
                ))
              )}
            </div>
            {hasNextPage && !isFetching && <InView as="div" onChange={inView => (inView ? fetchNextPage() : null)} />}
            {isFetching && (
              <div className="flex items-center justify-center py-4">
                <Loading size="xs" />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
