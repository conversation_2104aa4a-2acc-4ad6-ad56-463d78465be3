import { ICategoriesData } from "@/services/apis/home/<USER>";
import Image from "next/image";
import Link from "next/link";

interface ICategoryItemProps {
  category: ICategoriesData;
}

function CategoryItem({ category }: ICategoryItemProps) {
  return (
    <Link prefetch href={category?.id} key={category.id} className="flex flex-col items-center gap-2">
      <div className="relative flex aspect-square w-full items-center justify-center rounded-lg bg-surface-primary">
        <Image src={category.image || "/default-category.svg"} alt={category.name} className="object-contain" fill />
      </div>
      <span className="text-center text-xs text-content-primary">{category.name}</span>
    </Link>
  );
}

export default CategoryItem;
