"use client";

import Slider from "@/components/ui/slider";
import { useSlidersQuery } from "@/services/apis/slider/slider";
import Image from "next/image";
import React from "react";

function HomeSlider() {
  const { data, isLoading } = useSlidersQuery();

  if (isLoading) {
    return <div className="h-[172px] bg-surface-secondary" />;
  }

  if (!data?.data?.length) {
    return null;
  }

  return (
    <div className="relative w-full shrink-0 overflow-hidden">
      <Slider
        slides={
          data?.data?.map(item => (
            <div key={item?.id} className="relative h-[172px] w-full overflow-hidden rounded-md">
              <Image fill alt="" src={item.image} className="h-full w-full object-cover" />
            </div>
          )) || []
        }
        swiperProps={{
          slidesPerView: 1,
          spaceBetween: -20,
          centeredSlides: true
          //   centeredSlidesBounds: true,
        }}
      />
    </div>
  );
}

export default HomeSlider;
