"use client";

import React, { useState, useEffect } from "react";
import { twMerge } from "tailwind-merge";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.css";
import { Keyboard, Mousewheel, Navigation } from "swiper/modules";
import { InfiniteData } from "@tanstack/react-query";
import { TProductsResponse } from "@/services/apis/product/types";
import { useParams, useRouter, useSearchParams } from "next/navigation";

interface IFilterProps {
  products?: InfiniteData<TProductsResponse, unknown>;
}

function Filters({ products }: IFilterProps) {
  const params = useParams();
  const router = useRouter();
  const categoryId = params?.id;

  const searchParams = useSearchParams();
  const subCategoryId = searchParams.get("subCategoryId");

  const [initialSlide, setInitialSlide] = useState(0);
  const [swiperRef, setSwiperRef] = useState<any>(null);

  useEffect(() => {
    if (products?.pages && subCategoryId) {
      let index = 0;
      let found = false;

      products.pages?.forEach((page, pageIndex) => {
        page.data?.leaf_categories?.forEach((category, categoryIndex) => {
          if (category.id === subCategoryId) {
            index = pageIndex * page.data.leaf_categories.length + categoryIndex + 1; // +1 because we have an extra slide before categories
            found = true;
          }
        });
      });

      if (found) {
        setInitialSlide(index);
        if (swiperRef) {
          swiperRef.slideTo(index);
        }
      }
    }
  }, [products, subCategoryId, swiperRef]);

  return (
    <div className="">
      <Swiper
        dir="rtl"
        slidesPerView="auto"
        keyboard
        grabCursor
        spaceBetween={12}
        modules={[Navigation, Keyboard, Mousewheel]}
        onSwiper={(swiper: any) => setSwiperRef(swiper)}
        initialSlide={initialSlide}
      >
        <SwiperSlide className="max-w-fit">
          {/* <div className="flex w-fit items-center gap-1.5 rounded-full bg-surface-inverse px-3 py-1.5">
            <FiltersIcon />
            <span className="text-xs font-medium text-content-on-action-hover-1">{t("general.filters")}</span>
          </div> */}
        </SwiperSlide>
        {products?.pages?.map(pageItems =>
          pageItems?.data?.leaf_categories?.map(item => (
            <SwiperSlide key={item?.id} className="max-w-fit">
              <div
                onClick={() => router.push(`${categoryId}?subCategoryId=${item?.id}`)}
                className={twMerge(
                  "flex cursor-pointer items-center justify-center rounded-full border border-border-secondary px-3 py-1.5 text-center text-xs font-medium text-content-secondary",
                  subCategoryId === item?.id ? "border !border-surface-inverse text-content-primary" : ""
                )}
              >
                {item?.name}
              </div>
            </SwiperSlide>
          ))
        )}
      </Swiper>
    </div>
  );
}

export default Filters;
