"use client";

import BottomNavBar from "@/components/ui/bottomNavBar";
import React from "react";
import PwaInstallPrompt from "@/components/containers/serviceWorker/InstallPrompt";
import ArrowLeft from "@/assets/icons/arrow-left.svg";
import SquareArrow from "@/assets/icons/square-arrow.svg";
import EnamadIcon from "@/assets/icons/enamad.svg";
import User from "@/assets/icons/user.svg";
import Notification from "@/assets/icons/notification.svg";
import Instagram from "@/assets/icons/instagram.svg";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useActionSheet } from "@/components/ui/actionSheet/useActionSheetStore";
import Auth from "@/components/containers/auth/Auth";
import { useCategoriesQuery, useStoreQuery } from "@/services/apis/home/<USER>";
import Loading from "@/components/ui/loading";
import { useProfileQuery } from "@/services/apis/profile/profile";
import { handleRedirectSocialMedia } from "@/utils/helpers";
import Image from "next/image";
import { faqItems } from "./utils";
import SearchBar from "./SearchBar";
import CategoryItem from "./CategoryItem";

const Home: React.FC = () => {
  const t = useTranslations();
  const { open, close } = useActionSheet();

  const { data: categories, isLoading: isLoadingCategories } = useCategoriesQuery();
  const { data: profile, isLoading: isLoadingProfile } = useProfileQuery();
  const { data: store, isLoading: isLoadingStore } = useStoreQuery();

  const onOpenLogin = () => {
    open(
      <div>
        <Auth />
      </div>,
      {
        closable: false,
        fitHeight: true,
        bottomClose: true,
        backdropBlur: true,
        events: { onDidDismiss: () => close(), onBackdropTap: () => close() }
      }
    );
  };

  return (
    <div className="flex h-full flex-col">
      <div className="flex h-full flex-col overflow-y-auto">
        <div className="h-[76px] w-full shrink-0 bg-content-on-info" />

        <div className="-mt-9 rounded-[40px] bg-surface-primary px-4 py-3">
          <div className="flex flex-col">
            {isLoadingStore ? (
              <div className="flex items-center justify-center">
                <Loading size="xs" />
              </div>
            ) : (
              !!store?.data?.name && (
                <div className="mx-auto text-15 font-semibold text-content-on-info">{store?.data?.name}</div>
              )
            )}

            <div className="mt-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <User />
                {isLoadingProfile ? (
                  <div className="item-center flex justify-center">
                    <Loading size="xs" />
                  </div>
                ) : profile?.data?.id ? (
                  <span className="text-sm font-bold">{profile?.data?.name || t("home.noName")}</span>
                ) : (
                  <div className="flex cursor-pointer items-center gap-1" onClick={onOpenLogin}>
                    <span className="text-sm font-bold text-content-primary">{t("home.login")}</span>
                    <span className="text-sm font-bold text-content-subtle">|</span>
                    <span className="text-sm font-bold text-content-primary">{t("home.signUp")}</span>
                    <ArrowLeft />
                  </div>
                )}
              </div>

              <div>
                <Notification />
              </div>
            </div>
            <div className="mt-4">
              <SearchBar />
            </div>
          </div>
        </div>

        <div className="px-4 py-3">
          {/* <HomeSlider /> */}

          {isLoadingCategories ? (
            <div className="flex items-center justify-center">
              <Loading />
            </div>
          ) : !categories?.data?.length ? (
            <div className="flex h-full w-full items-center justify-center">
              <span className="whitespace-nowrap text-sm text-content-secondary">{t("home.noCategories")}</span>
            </div>
          ) : (
            <div className="flex flex-col gap-4">
              <div className="mt-5 grid w-full grid-cols-3 gap-x-2 gap-y-5">
                {categories?.data?.slice(0, 3)?.map(category => <CategoryItem category={category} />)}
              </div>

              <div className="mt-5 grid w-full grid-cols-4 gap-x-2 gap-y-5">
                {categories?.data?.slice(3)?.map(category => <CategoryItem category={category} />)}
              </div>
            </div>
          )}
        </div>

        <div className="mt-16 bg-black p-6 text-white">
          {isLoadingStore ? (
            <div className="flex items-center justify-center">
              <Loading size="xs" />
            </div>
          ) : (
            !!store?.data?.logo && (
              <div className="flex items-center justify-center">
                <Image
                  src={store?.data?.logo}
                  width={100}
                  height={100}
                  alt={store?.data?.name || ""}
                  className="size-[100px] object-contain"
                />
              </div>
            )
          )}
          <div className="mt-5">
            <PwaInstallPrompt />

            <div className="mt-5 flex flex-col">
              {faqItems?.map(item => (
                <Link
                  prefetch
                  href={`${item?.path}/${item?.type}`}
                  className="flex cursor-pointer items-center justify-between border-b border-b-border-secondary-light py-4 last:border-b-transparent"
                >
                  <span className="!text-13 font-medium text-content-on-action-hover-1">
                    {t(`home.footer.${item.type as keyof IntlMessages["home"]["footer"]}`)}
                  </span>
                  <ArrowLeft />
                </Link>
              ))}
            </div>
            <div className="mt-5 flex gap-3">
              {store?.data?.instagram_id && (
                <div
                  className="flex flex-1 cursor-pointer items-center justify-between rounded-lg bg-surface-secondary-light px-3 py-2.5"
                  onClick={() => handleRedirectSocialMedia({ platform: "instagram", sid: store?.data?.instagram_id })}
                >
                  <div className="flex items-center gap-2">
                    <div>
                      <Instagram className="h-9 w-9" />
                    </div>
                    <div className="flex flex-col gap-1">
                      <h3 className="text-sm font-semibold text-content-on-action-hover-1">
                        {t("home.instagram.title")}
                      </h3>

                      <span className="text-xs text-content-secondary-light">{store?.data?.instagram_id}</span>
                    </div>
                  </div>

                  <div>
                    <SquareArrow />
                  </div>
                </div>
              )}

              <div className="flex min-w-16 items-center justify-center rounded-lg bg-surface-secondary-light p-3">
                <EnamadIcon />
              </div>
            </div>

            <p className="mt-5 !text-center text-10 font-bold text-surface-primary">
              {t("home.footer.title", { value: store?.data?.name })}
            </p>
          </div>
        </div>

        {/* Navigation Bar */}
      </div>
      <BottomNavBar />
    </div>
  );
};

export default Home;
