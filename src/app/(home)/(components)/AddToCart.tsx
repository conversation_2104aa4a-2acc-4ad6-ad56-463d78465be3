import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useTranslations } from "next-intl";
import Trash from "@/assets/icons/trash.svg";
import Plus from "@/assets/icons/plus.svg";
import Cart from "@/assets/icons/mini-cart.svg";
import Minimize from "@/assets/icons/Minimize.svg";
import { useCartQuery } from "@/services/apis/cart/cart";
import { Variant } from "@/services/apis/product/types";
import { useCartModification } from "@/hooks/useCartModification";
import { useRouter } from "next/navigation";
import { SHOPPING_CART } from "@/constants/nextjsRoutes";

interface IAddToCartProps {
  variants: Variant[];
}

function AddToCart({ variants }: IAddToCartProps) {
  const t = useTranslations();
  const [cartCount, setCartCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const variant = variants?.[0];
  const router = useRouter();

  const { data: carts, isError: isCartError } = useCartQuery();

  const cart = carts?.data?.line_items?.find(item => item?.variant_id === variant?.id);

  const totalCartQuantity = isCartError
    ? 0
    : carts?.data?.line_items?.reduce((prev, current) => prev + (current?.quantity ?? 0), 0);

  // Function to show notification
  const showNotification = () => {
    setIsVisible(true);

    // Clear previous timeout before setting a new one
    if (timeoutId) clearTimeout(timeoutId);

    const id = setTimeout(() => {
      setIsVisible(false);
    }, 10000);
    setTimeoutId(id);
  };

  const { handleAddProduct, handleRemoveProduct } = useCartModification({
    variants,
    onChangeCartCount: setCartCount,
    showNotification
  });

  useEffect(
    () => () => {
      if (timeoutId) clearTimeout(timeoutId); // Cleanup timeout on unmount
    },
    [timeoutId]
  );

  useEffect(() => {
    if (cart?.variant_id) setCartCount(cart?.quantity);
  }, [cart]);

  return (
    <div className="flex flex-col items-center justify-center">
      <div
        className="flex items-center justify-center"
        onClick={e => {
          e.stopPropagation();
          e.preventDefault();
        }}
      >
        <AnimatePresence>
          {cartCount > 0 ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
              className="flex h-8 items-center gap-3 rounded-full bg-white py-2 pl-3 pr-2"
            >
              <div
                onClick={e => {
                  e.preventDefault();
                  e.stopPropagation();
                  setCartCount(cartCount + 1);
                  handleAddProduct(cartCount + 1);
                }}
                className="flex cursor-pointer items-center justify-center rounded-full bg-surface-primary"
              >
                <Plus />
              </div>
              <motion.span
                key={cartCount}
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.8, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="flex size-6 shrink-0 items-center justify-center rounded-full bg-surface-thertiary text-[18px] font-semibold text-content-on-info-2"
              >
                {cartCount}
              </motion.span>
              <button
                type="button"
                onClick={e => {
                  e.preventDefault();
                  e.stopPropagation();
                  setCartCount(cartCount - 1);
                  handleRemoveProduct(cartCount - 1);
                }}
                className="flex h-full items-center justify-center text-gray-500 hover:text-red-500"
              >
                {cartCount === 1 ? <Trash className="cursor-pointer" /> : <Minimize className="cursor-pointer" />}
              </button>
            </motion.div>
          ) : (
            <motion.button
              whileTap={{ scale: 1 }}
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();

                setCartCount(cartCount + 1);
                handleAddProduct(cartCount + 1);
              }}
              className="flex size-8 items-center justify-center rounded-full bg-surface-primary"
            >
              {/* <Plus size={20} className="text-white" /> */}

              <Plus />
            </motion.button>
          )}
        </AnimatePresence>
      </div>

      <div className="fixed bottom-8 left-1/2 z-50 -translate-x-1/2">
        <AnimatePresence>
          {isVisible && cartCount > 0 && (
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: 50, opacity: 0 }}
              onClick={e => {
                e.stopPropagation();
                e.preventDefault();
                router.push(SHOPPING_CART);
              }}
              transition={{ type: "spring", stiffness: 250, damping: 20 }}
              className="rounded-full bg-content-on-info-2 px-5 py-3 text-white shadow-sm"
              dir="rtl"
            >
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <Cart className="text-white" />
                  {totalCartQuantity && totalCartQuantity > 0 && (
                    <span className="text-lg font-bold">{totalCartQuantity}</span>
                  )}

                  <div className="mr-2 h-3.5 w-[1px] bg-white" />
                </div>
                <span className="whitespace-nowrap text-sm font-medium">{t("general.showCart")}</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}

export default AddToCart;
