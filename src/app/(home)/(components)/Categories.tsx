"use client";

import React, { useEffect, useRef, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Swiper, SwiperRef, SwiperSlide } from "swiper/react";
import Image from "next/image";
import { Keyboard, Mousewheel, Navigation } from "swiper/modules";
import { useCategoriesQuery } from "@/services/apis/home/<USER>";
import Loading from "@/components/ui/loading";
import { useParams, useRouter } from "next/navigation";
import "swiper/swiper-bundle.css";

function Categories() {
  const router = useRouter();
  const params = useParams();

  const [selectedCategoryId, setSelectedCategoryId] = useState(params?.id);
  const isActive = false;

  const { data: categories, isLoading: isLoadingCategories } = useCategoriesQuery();

  const swiperRef = useRef<SwiperRef>(null);

  const swipeToSlide = () => {
    if (categories?.data && selectedCategoryId) {
      const index = categories?.data?.findIndex(item => item.id === selectedCategoryId);
      if (index !== -1) {
        if (swiperRef?.current) {
          swiperRef?.current?.swiper.slideTo(index);
        }
      }
    }
  };

  useEffect(() => {
    if (selectedCategoryId !== params?.id) {
      setSelectedCategoryId(params?.id);
      swipeToSlide();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params?.id]);

  if (isLoadingCategories) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <Loading size="xs" />
      </div>
    );
  }

  return (
    <div className="pr-4">
      <Swiper
        dir="rtl"
        slidesPerView="auto"
        keyboard
        grabCursor
        spaceBetween={12}
        modules={[Navigation, Keyboard, Mousewheel]}
        ref={swiperRef}
        onSwiper={swipeToSlide}
        touchEventsTarget="container"
      >
        {categories?.data?.map(item => (
          <SwiperSlide key={item?.id} className="!h-auto max-w-[68px]">
            <div
              key={item?.id}
              onClick={() => {
                router.replace(`/${item?.id}`);
                setSelectedCategoryId(item?.id);
              }}
              className={twMerge("flex flex-col items-center gap-2")}
            >
              <div
                className={twMerge(
                  "relative flex size-[68px] items-center justify-center rounded-lg border-[2px] border-transparent bg-surface-info",
                  selectedCategoryId === item?.id ? "!border-content-on-action-2" : ""
                )}
              >
                <Image src={item.image || "/default-category.svg"} alt={item?.name} className="object-contain" fill />
              </div>
              <span className={twMerge("text-v2-content-primary text-center text-10", isActive ? "font-medium" : "")}>
                {item?.name}
              </span>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
}

export default Categories;
