import Providers from "@/providers";
import { IRANYekanXFaNumPro } from "@/providers/fonts";
import "@/styles/globals.css";
import type { Viewport } from "next";
import { getLocale } from "next-intl/server";
import { cookies } from "next/headers";
import { twMerge } from "tailwind-merge";
import { ToastContainer } from "react-toastify";
import { JsonLd } from "@/components/containers/JsonLd/JsonLd";

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover"
  // themeColor: [
  //   { media: "(prefers-color-scheme: dark)", color: "#000" },
  //   { media: "(prefers-color-scheme: light)", color: "#f1f2f6" },
  //   { color: "#000" }
  // ]
};

export default async function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  const locale = await getLocale();
  const cookiesStore = cookies();
  const themeInCookie = cookiesStore.get("theme")?.value;
  const darkClass = themeInCookie && themeInCookie === "dark" ? "dark" : "";

  return (
    <html lang={locale} dir="rtl" className={twMerge("mx-auto max-w-lg overscroll-y-none", darkClass)}>
      <body className={`${IRANYekanXFaNumPro.variable} !font-iran-yekan antialiased`}>
        <ToastContainer position="top-center" />
        <Providers>
          <JsonLd />
          <div className="h-full" style={{ background: "rgba(243, 244, 246, 1)" }}>
            {children}
            {/* <GeneralMetaTags /> */}
          </div>
        </Providers>
      </body>
    </html>
  );
}
