import { STORE_HANDLE } from "@/constants/cookies";
import { generalMetaTags } from "@/utils/generateGeneralMetaTags";
import { getData } from "@/utils/getServerCookie";
import React, { Suspense } from "react";
import SearchPage from "./SearchPage";

export async function generateMetadata() {
  const storeHandle = await getData(STORE_HANDLE);
  return generalMetaTags({ pageName: "جستجو", storeHandle });
}

function Page() {
  return (
    <Suspense>
      <SearchPage />
    </Suspense>
  );
}

export default Page;
