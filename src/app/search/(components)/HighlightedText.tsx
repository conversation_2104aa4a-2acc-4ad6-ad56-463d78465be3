/* eslint-disable react/no-array-index-key */
function HighlightedText({ text, searchString }: { text: string; searchString?: string | null }) {
  if (!searchString) return <span>{text}</span>;

  const parts = text.split(new RegExp(`(${searchString})`, "gi"));

  return (
    <>
      {parts.map((part, index) =>
        part.toLowerCase() === searchString.toLowerCase() ? (
          <span key={index} className="text-content-primary">
            {part}
          </span>
        ) : (
          <span key={index} className="text-content-secondary">
            {part}
          </span>
        )
      )}
    </>
  );
}

export default HighlightedText;
