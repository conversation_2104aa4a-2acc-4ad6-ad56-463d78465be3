/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import Loading from "@/components/ui/loading";
import React, { useEffect } from "react";
import { twMerge } from "tailwind-merge";
import { useSearchesQuery } from "@/services/apis/search/search";
import { parseAsString, useQueryStates } from "nuqs";
import useRecentSearches from "@/hooks/useRecentSearches";
import Product from "@/app/(home)/(components)/Product";
import { calculateDiscount } from "@/app/(home)/(components)/utils";
import { useTranslations } from "next-intl";
import RecentIcon from "@/assets/icons/recent-icon.svg";
import ClearSearch from "@/assets/icons/clear-icon.svg";
import SeacrhEmptyResult from "./SeacrhEmptyResult";

function SearchResult() {
  const t = useTranslations();
  const [queryParams, setQueryParams] = useQueryStates({
    searchQ: parseAsString
  });
  const { searches, removeSearch, addSearch } = useRecentSearches();

  const { data: searchData, isLoading, isSuccess } = useSearchesQuery({ query: queryParams?.searchQ || "" });

  useEffect(() => {
    if (queryParams?.searchQ) {
      addSearch(queryParams?.searchQ);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchData, queryParams?.searchQ]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-4">
        <Loading size="sm" />
      </div>
    );
  }

  if (isSuccess && !searchData?.data?.length && (!searches?.length || queryParams?.searchQ)) {
    return <SeacrhEmptyResult />;
  }

  if (searchData?.data?.length) {
    return (
      <div className="flex flex-col gap-3 px-4">
        <div className="flex items-center justify-between rounded-lg bg-surface-primary px-4 py-3">
          <span className="text-13 font-medium text-content-tertiary">{t("home.searchResult")}</span>
          <span className="text-13 font-semibold text-content-primary">
            {searchData?.data?.length} {t("general.product")}
          </span>
        </div>
        <div className="overflow-auto">
          <div className="grid grid-cols-2 gap-4">
            {searchData?.data?.map(item => (
              <Product
                id={item?.id}
                discountPercentage={
                  calculateDiscount(item?.cheapest_variant?.price, item?.cheapest_variant?.compare_at_price) ?? 0
                }
                variants={item?.variants}
                hasVariant={item?.has_variant}
                finalPrice={item?.cheapest_variant?.price}
                image={item?.cover?.url}
                originalPrice={item?.cheapest_variant?.compare_at_price}
                title={item?.name}
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 overflow-auto px-4">
      {!queryParams?.searchQ &&
        searches?.length &&
        Array.from(new Set(searches)).map(search => (
          <div
            key={search}
            className={twMerge("flex cursor-pointer items-start gap-4 text-15 font-normal text-content-secondary")}
            onClick={() => setQueryParams({ searchQ: search })}
          >
            <RecentIcon className="mt-1" />
            <div className="flex flex-1 items-center justify-between border-b border-b-border-primary pb-4">
              <p className="text-15 font-medium leading-[0]">{search}</p>
              <div
                onClick={e => {
                  e.stopPropagation();
                  removeSearch(search);
                }}
              >
                <ClearSearch />
              </div>
            </div>
          </div>
        ))}
    </div>
  );
}

export default SearchResult;
