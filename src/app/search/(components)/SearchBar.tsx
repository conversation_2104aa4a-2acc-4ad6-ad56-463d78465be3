"use client";

import React from "react";
import Link from "next/link";
import Input from "@/components/ui/input";
import { useTranslations } from "next-intl";
import { HOME } from "@/constants/nextjsRoutes";
import SearchIcon from "@/assets/icons/search.svg";
import InputWrapper from "@/components/ui/inputWrapper";
import MultiplicationSignCircleIcon from "@/assets/icons/multiplication-sign-circle.svg";

function SearchBar({ onChange, value }: { onChange: (v?: string | null) => void; value?: string }) {
  const t = useTranslations();

  return (
    <div className="flex items-center gap-3 border-b border-b-border-primary px-4 py-2">
      <InputWrapper
        variant="filled"
        rounded="md"
        className="flex-1"
        startAdornment={<SearchIcon className="size-5 shrink-0 [&>path]:stroke-gray-200" />}
        endAdornment={
          value && <MultiplicationSignCircleIcon className="shrink-0 cursor-pointer" onClick={() => onChange(null)} />
        }
      >
        <Input
          placeholder={t("general.searchWithDots")}
          autoFocus
          value={value}
          onChange={e => onChange(e.target.value)}
          className="flex-1"
        />
      </InputWrapper>

      <Link prefetch href={HOME} className="shrink-0">
        <div className="text-15 font-medium text-content-primary">{t("general.cancel")}</div>
      </Link>
    </div>
  );
}

export default SearchBar;
