"use client";

import React, { useState, useEffect } from "react";
import { parseAsString, useQueryStates } from "nuqs";
import { useDebounce } from "use-debounce";
import SearchBar from "./(components)/SearchBar";
import SearchResult from "./(components)/SearchResult";

function SearchPage() {
  const [queryParams, setQueryParams] = useQueryStates({ searchQ: parseAsString });
  const [searchInput, setSearchInput] = useState(queryParams?.searchQ || "");

  // Using the use-debounce hook with 500ms delay
  const [debouncedSearchValue] = useDebounce(searchInput, 500);

  // Handle immediate UI updates
  const handleSearchChange = (value: string | null | undefined) => {
    setSearchInput(value || "");
  };

  // Effect to handle the debounced search value
  useEffect(() => {
    if (!debouncedSearchValue) {
      setQueryParams({ searchQ: null });
    } else {
      setQueryParams({ searchQ: debouncedSearchValue });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedSearchValue]);

  // Sync input value with URL params on initial load
  useEffect(() => {
    if (queryParams?.searchQ !== undefined) {
      setSearchInput(queryParams.searchQ || "");
    }
  }, [queryParams?.searchQ]);

  // const handleClickCategoryItem = (id: number, label: string) => {
  //   setQueryParams({
  //     categoryId: id,
  //     categoryLabel: label,
  //     searchQ: null
  //   });
  // };

  return (
    <div className="flex h-full w-full flex-col gap-3 bg-white pt-2.5">
      <SearchBar onChange={handleSearchChange} value={searchInput} />

      <SearchResult />
    </div>
  );
}

export default SearchPage;
